import { md5 } from 'js-md5';
import { nanoid } from 'nanoid';
import { NativeFile } from '@/helps/native-file';
import { API_SALT } from '@/config/api';
import { namespaceT } from '@/helps/namespace-t';
import { valueByLocale } from './locale';

const t = namespaceT();

const Locale = Object.freeze({
  ZH_MO: 'zh_MO',
  EN_US: 'en_US',
});

export function getFileNameSuffix(fileName: string) {
  return fileName.split('.')[1];
}

function getDownloadUrl(remoteUrl: string) {
  const lang = valueByLocale(Locale.ZH_MO, Locale.EN_US);
  const nonce = md5(nanoid());
  const str = md5(`lang=${lang}nonce=${nonce}${API_SALT}`);
  const href = `${process.env.AI_MARY_API_BASE_URL}${remoteUrl}?lang=${lang}&nonce=${nonce}&signature=${str}`;
  return href;
}

export function downloadFile(remoteUrl: string): Promise<{ fileName: string, status: number }> {
  const fileUrl = getDownloadUrl(remoteUrl);
  return new Promise((resolve) => {
    const download = native.downloader.createDownload(
      fileUrl,
      {
        method: 'GET',
        filename: `${t('text.fileName')}.PDF`,
      },
      (downloadData: { filename:string }, status: number) => {
        resolve({ fileName: downloadData.filename, status });
      },
    );
    download.setRequestHeader('Authorization', native.user.getAccessToken());
    download.start();
  });
}

export function downloadAllTypeFile(remoteUrl: string, name: string): Promise<{ fileName: string, status: number }> {
  const fileUrl = getDownloadUrl(remoteUrl);
  return new Promise((resolve) => {
    const download = native.downloader.createDownload(
      fileUrl,
      {
        method: 'GET',
        filename: name,
      },
      (downloadData: { filename:string }, status: number) => {
        resolve({ fileName: downloadData.filename, status });
      },
    );
    download.setRequestHeader('Authorization', native.user.getAccessToken());
    download.start();
  });
}

export async function previewFile({ fileUrl }: { fileUrl: string }) {
  const nativeFile = new NativeFile({ limit: 20 * 1024 * 1024 });
  nativeFile.previewFile(fileUrl);
}

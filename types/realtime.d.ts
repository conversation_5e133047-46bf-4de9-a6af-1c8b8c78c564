import type { RoleType } from '@/consts/role-type';
import type { CustomEventName } from '@/consts/custom-event-name';

declare global {
  namespace Realtime {
    /**
     * Realtime API 基础消息接口
     * 所有 Realtime API 消息的基础类型
     */
    interface Message {
      /** 消息类型标识符 */
      type: string;
      /** 其他动态属性 */
      [key: string]: unknown;
    }

    /**
     * 会话更新命令接口
     * 用于配置 Realtime 会话参数
     */
    interface SessionUpdateCommand {
      type: 'session.update';
      session: {
        /** 语音活动检测配置 */
        turn_detection?: {
          /** 检测类型：服务器端检测或无检测 */
          type: 'server_vad' | 'none';
        };
        /** 输入音频转录配置 */
        input_audio_transcription?: {
          /** 转录模型 */
          model: 'whisper-1';
        };
        modalities?: string[];
      };
    }

    /**
     * 对话项截断命令接口
     * 用于取消 AI 响应生成
     */
    interface ConversationItemTruncateCommand {
      type: 'conversation.item.truncate';
      item_id: string;
      content_index: number;
      audio_end_ms?: number;
    }

    /**
     * 音频缓冲区追加命令接口
     * 用于向输入音频缓冲区添加音频数据
     */
    interface InputAudioBufferAppendCommand {
      type: 'input_audio_buffer.append';
      /** Base64 编码的音频数据 */
      audio: string;
    }

    /**
     * 音频缓冲区清空命令接口
     * 用于清空输入音频缓冲区
     */
    interface InputAudioBufferClearCommand {
      type: 'input_audio_buffer.clear';
    }

    /**
     * 对话项创建命令接口
     * 用于创建新的对话消息项
     */
    interface ConversationItemCreateCommand {
      type: 'conversation.item.create';
      item: {
        type: 'message';
        role: RoleType;
        content: {
          type: 'input_text';
          /** 文本内容 */
          text: string;
        }[];
      };
    }

    /**
     * 响应创建命令接口
     * 用于触发 AI 响应生成
     */
    interface ResponseCreateCommand {
      type: 'response.create';
    }

    /**
     * 响应音频增量接口
     * 包含 AI 响应的音频数据片段
     */
    interface ResponseAudioDelta {
      type: 'response.audio.delta';
      /** 事件 ID */
      event_id: string;
      /** 响应 ID */
      response_id: string;
      /** 项目 ID */
      item_id: string;
      /** 输出索引 */
      output_index: number;
      /** 内容索引 */
      content_index: number;
      /** Base64 编码的音频数据增量 */
      delta: string;
    }

    /**
     * 响应音频转录增量接口
     * 包含 AI 响应的转录文本片段
     */
    interface ResponseAudioTranscriptDelta {
      type: 'response.audio_transcript.delta';
      /** 事件 ID */
      event_id: string;
      /** 响应 ID */
      response_id: string;
      /** 项目 ID */
      item_id: string;
      /** 输出索引 */
      output_index: number;
      /** 内容索引 */
      content_index: number;
      /** 转录文本增量 */
      delta: string;
    }

    /**
     * 响应文本增量接口
     * 包含 AI 响应的文本内容片段
     */
    interface ResponseTextDelta {
      type: 'response.text.delta';
      /** 事件 ID */
      event_id: string;
      /** 响应 ID */
      response_id: string;
      /** 项目 ID */
      item_id: string;
      /** 输出索引 */
      output_index: number;
      /** 内容索引 */
      content_index: number;
      /** 文本增量 */
      delta: string;
    }

    /**
     * 响应文本完成接口
     * AI 响应的文本内容生成完成
     */
    interface ResponseTextDone {
      type: 'response.text.done';
      /** 事件 ID */
      event_id: string;
      /** 响应 ID */
      response_id: string;
      /** 项目 ID */
      item_id: string;
      /** 输出索引 */
      output_index: number;
      /** 内容索引 */
      content_index: number;
      /** 完整的文本内容 */
      text: string;
    }

    /**
     * 输入音频转录完成接口
     * 用户音频转录完成时的消息
     */
    interface ResponseInputAudioTranscriptionCompleted {
      type: 'conversation.item.input_audio_transcription.completed';
      /** 事件 ID */
      event_id: string;
      /** 项目 ID */
      item_id: string;
      /** 内容索引 */
      content_index: number;
      /** 完整的转录文本 */
      transcript: string;
    }

    /**
     * 响应完成接口
     * AI 响应生成完成时的消息
     */
    interface ResponseDone {
      type: 'response.done';
      /** 事件 ID */
      event_id: string;
      response: {
        /** 响应 ID */
        id: string;
        /** 输出内容数组 */
        output: { id: string; status: string; content?: { transcript: string; type: string }[] }[];
      };
    }

    /**
     * 会话创建接口
     */
    interface SessionCreated {
      type: 'session.created';
      event_id: string;
      session: {
        id: string;
        object: string;
        model: string;
        expires_at: number;
        modalities: string[];
        instructions: string;
        voice: string;
        input_audio_format: string;
        output_audio_format: string;
        input_audio_transcription?: {
          model: string;
        };
        turn_detection: {
          type: string;
          threshold?: number;
          prefix_padding_ms?: number;
          silence_duration_ms?: number;
        };
        tools: unknown[];
        tool_choice: string;
        temperature: number;
        max_response_output_tokens: number | string;
      };
    }

    /**
     * 会话更新接口
     */
    interface SessionUpdated {
      type: 'session.updated';
      event_id: string;
      session: {
        id: string;
        object: string;
        model: string;
        expires_at: number;
        modalities: string[];
        instructions: string;
        voice: string;
        input_audio_format: string;
        output_audio_format: string;
        input_audio_transcription?: {
          model: string;
        };
        turn_detection: {
          type: string;
          threshold?: number;
          prefix_padding_ms?: number;
          silence_duration_ms?: number;
        };
        tools: unknown[];
        tool_choice: string;
        temperature: number;
        max_response_output_tokens: number | string;
      };
    }

    /**
     * 输入音频缓冲区语音停止接口
     */
    interface InputAudioBufferSpeechStopped {
      type: 'input_audio_buffer.speech_stopped';
      event_id: string;
      audio_end_ms: number;
      item_id: string;
    }

    /**
     * 输入音频缓冲区提交接口
     */
    interface InputAudioBufferCommitted {
      type: 'input_audio_buffer.committed';
      event_id: string;
      previous_item_id: string;
      item_id: string;
    }

    /**
     * 对话项创建接口
     */
    interface ConversationItemCreated {
      type: 'conversation.item.created';
      event_id: string;
      previous_item_id?: string;
      item: {
        id: string;
        object: string;
        type: string;
        status: string;
        role: RoleType;
        content: unknown[];
      };
    }

    /**
     * 响应创建接口
     */
    interface ResponseCreated {
      type: 'response.created';
      event_id: string;
      response: {
        id: string;
        object: string;
        status: string;
        status_details?: unknown;
        output: unknown[];
        usage?: unknown;
      };
    }

    /**
     * 响应输出项添加接口
     */
    interface ResponseOutputItemAdded {
      type: 'response.output_item.added';
      event_id: string;
      response_id: string;
      output_index: number;
      item: {
        id: string;
        object: string;
        type: string;
        status: string;
        role: RoleType;
        content: unknown[];
      };
    }

    /**
     * 响应内容部分添加接口
     */
    interface ResponseContentPartAdded {
      type: 'response.content_part.added';
      event_id: string;
      response_id: string;
      item_id: string;
      output_index: number;
      content_index: number;
      part: {
        type: string;
        audio?: string;
        transcript?: string;
        text?: string;
      };
    }

    /**
     * 响应内容部分完成接口
     */
    interface ResponseContentPartDone {
      type: 'response.content_part.done';
      event_id: string;
      response_id: string;
      item_id: string;
      output_index: number;
      content_index: number;
      part: {
        type: string;
        audio?: string;
        transcript?: string;
        text?: string;
      };
    }

    /**
     * 响应输出项完成接口
     */
    interface ResponseOutputItemDone {
      type: 'response.output_item.done';
      event_id: string;
      response_id: string;
      output_index: number;
      item: {
        id: string;
        object: string;
        type: string;
        status: string;
        role: RoleType;
        content: unknown[];
      };
    }

    /**
     * 响应音频完成接口
     */
    interface ResponseAudioDone {
      type: 'response.audio.done';
      event_id: string;
      response_id: string;
      item_id: string;
      output_index: number;
      content_index: number;
    }

    /**
     * 响应音频转录完成接口
     */
    interface ResponseAudioTranscriptDone {
      type: 'response.audio_transcript.done';
      event_id: string;
      response_id: string;
      item_id: string;
      output_index: number;
      content_index: number;
      transcript: string;
    }

    /**
     * 速率限制更新接口
     */
    interface RateLimitsUpdated {
      type: 'rate_limits.updated';
      event_id: string;
      rate_limits: {
        name: string;
        limit: number;
        remaining: number;
        reset_seconds: number;
      }[];
    }

    /**
     * 工具调用开始接口
     */
    interface RunStarted {
      type: 'RUN_STARTED';
      timestamp: null;
      raw_event: null;
      thread_id: string;
      run_id: string;
    }

    /**
     * 工具调用完成接口
     */
    interface RunFinished {
      type: 'RUN_FINISHED';
      timestamp: null;
      raw_event: null;
      thread_id: string;
      run_id: string;
    }

    /**
     * OPEN_APP 事件的 value 类型
     */
    interface OpenAppEventValue {
      /** 服务编码 */
      serviceCode: string;
    }

    /**
     * 自定义事件类型映射
     * 根据 name 的不同，value 的类型也不同
     */
    interface CustomEventValueMap {
      [CustomEventName.OPEN_APP]: OpenAppEventValue;
    }

    /**
     * 自定义事件接口
     */
    type CustomEvent = {
      [K in keyof CustomEventValueMap]: {
        type: 'CUSTOM_EVENT';
        timestamp: null;
        raw_event: null;
        name: K;
        value: CustomEventValueMap[K];
      };
    }[keyof CustomEventValueMap];

    /**
     * Realtime API 配置参数接口
     * 用于配置 WebSocket 连接和各种事件回调函数
     */
    interface Config {
      /** WebSocket 连接端点 URL，支持相对路径、完整 URL 或 http/https 自动转换 */
      wsEndpoint?: string;

      /** 是否启用输入音频转录功能，默认为 true */
      enableInputAudioTranscription?: boolean;

      /** 是否自动连接，默认为 true */
      autoConnect?: boolean;

      /** 最大重试次数，默认为 5 */
      maxRetries?: number;

      /** 重连延迟时间(毫秒)，默认为 3000 */
      reconnectDelay?: number;

      // WebSocket 基础事件回调
      /** WebSocket 连接成功时的回调函数 */
      onWebSocketOpen?: () => void;

      /** WebSocket 连接关闭时的回调函数 */
      onWebSocketClose?: () => void;

      /** WebSocket 连接错误时的回调函数 */
      onWebSocketError?: (event: Event) => void;

      // 音频相关回调
      /** 接收到响应音频数据增量时的回调函数 */
      onReceivedResponseAudioDelta?: (message: ResponseAudioDelta) => void;

      /** 检测到用户开始说话时的回调函数 */
      onReceivedInputAudioBufferSpeechStarted?: (message: Message) => void;

      /** 响应完成时的回调函数 */
      onReceivedResponseDone?: (message: ResponseDone) => void;

      /** 接收到响应音频转录增量时的回调函数 */
      onReceivedResponseAudioTranscriptDelta?: (message: ResponseAudioTranscriptDelta) => void;

      /** 接收到响应文本增量时的回调函数 */
      onReceivedResponseTextDelta?: (message: ResponseTextDelta) => void;

      /** 接收到响应文本完成时的回调函数 */
      onReceivedResponseTextDone?: (message: ResponseTextDone) => void;

      /** 输入音频转录完成时的回调函数 */
      onReceivedInputAudioTranscriptionCompleted?: (message: ResponseInputAudioTranscriptionCompleted) => void;

      /** 接收到错误消息时的回调函数 */
      onReceivedError?: (message: Message) => void;

      // 会话相关回调
      /** 会话创建成功时的回调函数 */
      onReceivedSessionCreated?: (message: SessionCreated) => void;

      /** 会话更新成功时的回调函数 */
      onReceivedSessionUpdated?: (message: SessionUpdated) => void;

      // 音频缓冲区相关回调
      /** 检测到用户停止说话时的回调函数 */
      onReceivedInputAudioBufferSpeechStopped?: (message: InputAudioBufferSpeechStopped) => void;

      /** 音频缓冲区提交完成时的回调函数 */
      onReceivedInputAudioBufferCommitted?: (message: InputAudioBufferCommitted) => void;

      /** 音频缓冲区清空时的回调函数 */
      onReceivedInputAudioBufferCleared?: (message: Message) => void;

      // 对话相关回调
      /** 对话项创建成功时的回调函数 */
      onReceivedConversationItemCreated?: (message: ConversationItemCreated) => void;

      // 响应生成相关回调
      /** 响应创建成功时的回调函数 */
      onReceivedResponseCreated?: (message: ResponseCreated) => void;

      /** 响应输出项添加时的回调函数 */
      onReceivedResponseOutputItemAdded?: (message: ResponseOutputItemAdded) => void;

      /** 响应内容部分添加时的回调函数 */
      onReceivedResponseContentPartAdded?: (message: ResponseContentPartAdded) => void;

      /** 响应内容部分完成时的回调函数 */
      onReceivedResponseContentPartDone?: (message: ResponseContentPartDone) => void;

      /** 响应输出项完成时的回调函数 */
      onReceivedResponseOutputItemDone?: (message: ResponseOutputItemDone) => void;

      /** 响应音频完成时的回调函数 */
      onReceivedResponseAudioDone?: (message: ResponseAudioDone) => void;

      /** 响应音频转录完成时的回调函数 */
      onReceivedResponseAudioTranscriptDone?: (message: ResponseAudioTranscriptDone) => void;

      /** 工具调用开始时的回调函数 */
      onRunStarted?: (message: RunStarted) => void;

      /** 工具调用完成时的回调函数 */
      onRunFinished?: (message: RunFinished) => void;

      /** 自定义事件回调函数 */
      onCustomEvent?: (message: CustomEvent) => void;

      /** 速率限制更新时的回调函数 */
      onReceivedRateLimitsUpdated?: (message: RateLimitsUpdated) => void;
    }
  }
}

export {};
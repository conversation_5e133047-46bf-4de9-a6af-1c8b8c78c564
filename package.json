{"name": "wm-ai-mary-app-js", "version": "0.1.0", "private": true, "description": "AI Mary", "author": "<EMAIL>", "license": "ISC", "scripts": {"build": "yarn run build:client && yarn run build:app", "build:client": "vue-tsc --noEmit && cross-env NODE_ENV=production webpack --config ./build/webpack.production.js --mode production --progress", "build:app": "webpack --config ./build/webpack.app.js --mode production", "server": "webpack-cli serve --config ./build/webpack.production.js --mode production --progress", "dev": "vue-tsc --noEmit && cross-env NODE_ENV=development webpack-cli serve --config ./build/webpack.development.js --progress --hot", "analyze": "webpack --config ./build/webpack.production.js --mode production --progress --env report", "lint": "vue-tsc --noEmit && eslint --ext .js --ext .ts --ext .vue ./", "lint:css": "stylelint ./src/**/*.{css,less,vue}"}, "dependencies": {"@intlify/devtools-types": "^10.0.5", "@mdx-js/mdx": "^3.1.0", "@types/json-bigint": "^1.0.4", "@types/lodash": "^4.17.13", "@types/node": "^16.18.119", "@types/sanitize-html": "^2.13.0", "@types/urijs": "^1.19.25", "@vant/auto-import-resolver": "^1.2.1", "@vueuse/core": "^13.3.0", "amfe-flexible": "^2.2.1", "axios": "^1.8.4", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.39.0", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "dotenv-webpack": "^8.1.0", "eruda": "^3.4.1", "esbuild-loader": "~4.2.0", "highlight.js": "^11.11.1", "html-webpack-plugin": "^5.5.3", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "less-loader": "^11.1.2", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.7.6", "mitt": "^3.0.1", "nanoid": "^3.3.8", "node-polyfill-webpack-plugin": "^4.0.0", "p-queue": "^6.6.2", "pinia": "^2.2.6", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.3.0", "postcss-pxtorem": "^6.1.0", "rehype-external-links": "^3.0.0", "rehype-highlight": "^7.0.2", "sanitize-html": "^2.14.0", "thread-loader": "^4.0.2", "tslib": "^2.8.1", "unplugin-auto-import": "^0.18.5", "unplugin-vue-components": "^0.27.4", "urijs": "^1.19.11", "uuid": "^9.0.1", "vant": "^4.9.4", "vue": "^3.5.11", "vue-i18n": "^10.0.6", "vue-loader": "^17.4.2", "vue-router": "^4.4.3", "vue-tsc": "^2.1.10", "webpack": "^5.98.0", "webpack-cli": "^5.1.4", "webpack-merge": "^5.10.0"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.18.1", "eslint-webpack-plugin": "^4.0.1", "http-proxy-middleware": "^2.0.9", "less": "^4.2.0", "postcss": "~8.4.49", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "stylelint": "^15.11.0", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^2.0.0", "stylelint-order": "^6.0.4", "stylelint-webpack-plugin": "^4.1.1", "typescript": "^5.6.3", "vue-style-loader": "^4.1.3", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-server": "^4.15.2"}, "resolutions": {"@types/node": "^22.9.1", "braces": "^3.0.3", "cross-spawn": "^7.0.6", "elliptic": "^6.6.1", "express": "^4.21.2", "esbuild": "0.21.0", "follow-redirects": "^1.15.9", "micromatch": "^4.0.8", "postcss/nanoid": "^3.3.8", "ws": "^8.18.0"}, "engines": {"node": ">=16.18.0"}}
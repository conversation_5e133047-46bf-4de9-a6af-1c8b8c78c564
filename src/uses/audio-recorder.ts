export interface AudioData {
  data: string;
  sampleRate: number;
  bitsPerChannel: number;
  channels: number;
  duration: number;
  volumeRMS: number;
}

interface Parameters {
  onAudioRecorded: (base64Audio: string, volume: number) => void;
}

export function useAudioRecorder({ onAudioRecorded }: Parameters) {
  function handleAudioData(data: AudioData) {
    const { data: base64Audio, volumeRMS } = data;
    // 放大音量动画效果
    const volume = volumeRMS * 100 * 3;

    onAudioRecorded(base64Audio, volume);
  }

  async function start() {
    native.audio.startRecording({
      onUpdate: (data: AudioData) => {
        handleAudioData(data);
      },
    });
  }

  async function stop() {
    native.audio.stopRecording();
  }

  return {
    start,
    stop,
  };
}

import { AxiosInstance, RawAxiosRequestHeaders } from 'axios';

import { createRequest, type CreateRequestOptions } from '@/utils/request';
import { errorInterceptor } from './error-interceptor';


export type RequestConfig = CreateRequestOptions;

export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS' | 'CONNECT' | 'TRACE';

export type RequestParams = Record<string, unknown> | unknown[];

export type RequestData = Record<string, unknown> | unknown[] | FormData;

export class BaseRequestApi<T> {
  private request: AxiosInstance;

  private targetParams: RequestParams;

  private targetData: RequestData;

  constructor(args?: RequestConfig) {
    let baseURL;
    if (args && 'baseURL' in args) {
      ({ baseURL } = args);
    }

    let timeout;
    if (args && 'timeout' in args) {
      ({ timeout } = args);
    }

    let responseType;
    if (args && 'responseType' in args) {
      ({ responseType } = args);
    }

    let headers: RawAxiosRequestHeaders = {};
    if (args && 'headers' in args && args.headers !== undefined) {
      ({ headers } = args);
    }

    let salt;
    if (args && 'salt' in args && args.salt !== undefined) {
      ({ salt } = args);
    }

    this.request = createRequest({
      baseURL,
      timeout,
      responseType,
      headers: {
        ...headers,
        Authorization: window.native.user.getAccessToken(),
      },
      salt,
    }) as AxiosInstance;
    this.request.interceptors.response.use(
      (response) => response,
      (error) => errorInterceptor(error),
    );

    this.targetParams = this.defaultParams();
    this.targetData = this.defaultData();
  }

  method(): RequestMethod {
    return 'GET';
  }

  url(): string {
    return '';
  }

  defaultParams(): RequestParams {
    return {};
  }

  get params(): RequestParams {
    return this.targetParams;
  }

  set params(value: RequestParams) {
    this.targetParams = {
      ...this.defaultParams(),
      ...value,
    };
  }

  defaultData(): RequestData {
    return {};
  }

  get data(): RequestData {
    return this.targetData;
  }

  set data(value) {
    if (value instanceof FormData) {
      this.targetData = value;
      return;
    }

    if (Array.isArray(value)) {
      let defaultData = this.defaultData();
      defaultData = Array.isArray(defaultData) ? defaultData : [defaultData];
      this.targetData = [
        ...defaultData as unknown[],
        ...value,
      ];
      return;
    }

    this.targetData = {
      ...this.defaultData(),
      ...value,
    };
  }

  send(): Promise<T> {
    const method = this.method().toUpperCase();
    const isGetOrHeadMethod = ['GET', 'HEAD'].includes(method);
    const data = isGetOrHeadMethod ? undefined : this.targetData;

    return this.request({
      method,
      data,
      url: this.url(),
      params: this.targetParams,
    });
  }
}

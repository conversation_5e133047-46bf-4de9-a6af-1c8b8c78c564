import { useRecorder } from './recorder';
import { calcVolume } from '../utils/calc-volume';

interface Parameters {
  onAudioRecorded: (base64Audio: string, volume: number) => void;
}

export function useAudioRecorder({ onAudioRecorded }: Parameters) {
  const BUFFER_SIZE = 4800;
  let audioRecorder: ReturnType<typeof useRecorder> | null = null;
  let buffer = new Uint8Array();

  function appendToBuffer(newData: Uint8Array) {
    const newBuffer = new Uint8Array(buffer.length + newData.length);
    newBuffer.set(buffer);
    newBuffer.set(newData, buffer.length);
    buffer = newBuffer;
  }

  function handleAudioData(data: Iterable<number>) {
    const uint8Array = new Uint8Array(data);
    appendToBuffer(uint8Array);

    if (buffer.length >= BUFFER_SIZE) {
      const toSend = new Uint8Array(buffer.slice(0, BUFFER_SIZE));
      buffer = new Uint8Array(buffer.slice(BUFFER_SIZE));

      // @ts-expect-error
      const regularArray = String.fromCharCode(...toSend);
      const base64 = btoa(regularArray);

      onAudioRecorded(base64, calcVolume(base64));
    }
  }

  async function start() {
    if (!audioRecorder) {
      audioRecorder = useRecorder({ onDataAvailable: handleAudioData });
    }

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    audioRecorder.start(stream);
  }

  async function stop() {
    await audioRecorder?.stop();
    buffer = new Uint8Array();
  }

  return {
    start,
    stop,
  };
}

<script setup lang="ts">
import ToastLoading from '@/components/common/toast-loading.vue';
import Content from './components/content';
import Footer from './components/footer';

import { Align } from '@/consts/align';
import { ChatMode } from '@/consts/chat-mode';


interface ChatProps {
  // 聊天消息
  messages: Chat.Message[];
  // 当前聊天模式
  currentMode: ChatMode;
  // 聊天对齐方式
  align?: Align;
  // 是否正在连接
  isConnecting: boolean;
  // 是否连接错误
  isConnectError: boolean;
  // 是否静音
  isRecording: boolean;
  // AI是否正在回复
  isAiReplying: boolean;
  // 音量
  volume: number;
  contentLoading?: boolean;
}

withDefaults(defineProps<ChatProps>(), {
  align: Align.LEFT_RIGHT,
  contentLoading: false,
});

const emits = defineEmits<{
  'on-send-message': [message: string];
  'on-stop-message': [];
  'on-interrupt-ai-replying': [];
  'on-toggle-recording': [];
  'on-switch-mode': [mode: ChatMode];
  'on-load-history': [];
}>();

const historyLoading = defineModel<boolean>('historyLoading', {
  default: false,
});
const inputValue = defineModel<string>('input-value', {
  default: '',
});

function onSendMessage(message: string) {
  emits('on-send-message', message);
}

function onSwitchMode(mode: ChatMode) {
  emits('on-switch-mode', mode);
}

function onToggleRecording() {
  emits('on-toggle-recording');
}

function onInterruptAiReply() {
  emits('on-interrupt-ai-replying');
}
</script>


<template>
  <div class="chat">
    <div class="chat-body">
      <ToastLoading
        v-if="contentLoading"
      />

      <div class="chat-content">
        <Content
          v-model:history-loading="historyLoading"
          :messages="messages"
          :align="align"
          @on-load-history="emits('on-load-history')"
        />
      </div>

      <div class="chat-footer">
        <Footer
          v-model:input-value="inputValue"
          :current-mode="currentMode"
          :volume="volume"
          :is-connecting="isConnecting"
          :is-connect-error="isConnectError"
          :is-recording="isRecording"
          :is-ai-replying="isAiReplying"
          @on-send-message="onSendMessage"
          @on-toggle-recording="onToggleRecording"
          @on-switch-mode="onSwitchMode"
          @on-interrupt-ai-replying="onInterruptAiReply"
        />
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.chat {
  height: 100%;

  .chat-body {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;

    .chat-content {
      flex: 1;
      overflow: hidden auto;
    }
  }
}

:deep(.van-overlay) {
  position: absolute;
}
</style>

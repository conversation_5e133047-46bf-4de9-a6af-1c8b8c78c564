/**
 * 对话系统标准化类型定义
 * 定义通用的对话交互接口，支持多种大模型提供商和交互方式
 */

import type { RoleType } from '@/consts/role-type';
import type { MessageStatus } from '@/consts/message-status';
import type { ChatMode } from '@/consts/chat-mode';

declare global {
  namespace Conversation {
    // ==================== 连接层类型 ====================
    
    /**
     * 连接状态枚举
     */
    const enum ConnectionState {
      DISCONNECTED = 'disconnected',
      CONNECTING = 'connecting',
      CONNECTED = 'connected',
      ERROR = 'error',
      RECONNECTING = 'reconnecting'
    }

    /**
     * 连接配置接口
     */
    interface ConnectionConfig {
      /** 连接端点 */
      endpoint: string;
      /** 是否自动连接 */
      autoConnect?: boolean;
      /** 最大重试次数 */
      maxRetries?: number;
      /** 重连延迟 */
      reconnectDelay?: number;
      /** 连接超时时间 */
      timeout?: number;
      /** 自定义头部 */
      headers?: Record<string, string>;
    }

    /**
     * 连接事件接口
     */
    interface ConnectionEvents {
      onStateChange?: (state: ConnectionState) => void;
      onError?: (error: Error) => void;
      onMessage?: (message: unknown) => void;
    }

    // ==================== 交互层类型 ====================

    /**
     * 交互类型枚举
     */
    const enum InteractionType {
      VOICE = 'voice',
      TEXT = 'text',
      MIXED = 'mixed'
    }

    /**
     * 语音交互事件
     */
    interface VoiceInteractionEvents {
      /** 用户开始说话 */
      onUserSpeechStart?: () => void;
      /** 用户停止说话 */
      onUserSpeechEnd?: () => void;
      /** 用户语音转录完成 */
      onUserTranscriptionComplete?: (transcript: string, messageId: string) => void;
      /** AI开始语音回复 */
      onAiSpeechStart?: (messageId: string) => void;
      /** AI语音数据增量 */
      onAiAudioDelta?: (audioData: string, messageId: string) => void;
      /** AI语音转录增量 */
      onAiTranscriptDelta?: (transcript: string, messageId: string) => void;
      /** AI语音回复完成 */
      onAiSpeechComplete?: (messageId: string) => void;
    }

    /**
     * 文本交互事件
     */
    interface TextInteractionEvents {
      /** AI文本回复开始 */
      onAiTextStart?: (messageId: string) => void;
      /** AI文本增量 */
      onAiTextDelta?: (text: string, messageId: string) => void;
      /** AI文本回复完成 */
      onAiTextComplete?: (messageId: string) => void;
    }

    /**
     * 音频控制接口
     */
    interface AudioControl {
      /** 播放音频 */
      play: (audioData: string, isLast?: boolean) => void;
      /** 停止播放 */
      stop: () => void;
      /** 重置播放器 */
      reset: () => void;
      /** 是否正在播放 */
      isPlaying: () => boolean;
    }

    /**
     * 录音控制接口
     */
    interface RecordingControl {
      /** 开始录音 */
      start: () => void;
      /** 停止录音 */
      stop: () => void;
      /** 是否正在录音 */
      isRecording: () => boolean;
      /** 获取当前音量 */
      getVolume: () => number;
    }

    // ==================== 业务层类型 ====================

    /**
     * 消息接口（扩展原有Chat.Message）
     */
    interface Message extends Chat.Message {
      /** 消息来源交互类型 */
      interactionType?: InteractionType;
      /** 是否包含音频 */
      hasAudio?: boolean;
      /** 音频数据 */
      audioData?: string;
      /** 关联的父消息ID */
      parentMessageId?: string;
    }

    /**
     * 对话状态接口
     */
    interface ConversationState {
      /** 当前对话模式 */
      mode: ChatMode;
      /** 是否正在连接 */
      isConnecting: boolean;
      /** 连接是否出错 */
      hasConnectionError: boolean;
      /** AI是否正在回复 */
      isAiReplying: boolean;
      /** 是否正在录音 */
      isRecording: boolean;
      /** 是否静音 */
      isMuted: boolean;
      /** 当前音量 */
      volume: number;
      /** 文本输入内容 */
      inputText: string;
    }

    /**
     * 消息管理接口
     */
    interface MessageManager {
      /** 消息列表 */
      messages: Message[];
      /** 添加用户消息 */
      addUserMessage: (content: string, messageId?: string, status?: MessageStatus) => Message;
      /** 添加AI消息 */
      addAiMessage: (messageId: string, previousMessageId?: string) => Message;
      /** 更新消息内容 */
      updateMessageContent: (messageId: string, content: string) => void;
      /** 更新消息状态 */
      updateMessageStatus: (messageId: string, status: MessageStatus) => void;
      /** 删除消息 */
      removeMessage: (messageId: string) => void;
      /** 获取当前AI回复消息 */
      getCurrentAiMessage: () => Message | null;
      /** 清理空消息 */
      clearEmptyMessages: () => void;
    }

    /**
     * 响应渲染配置
     */
    interface ResponseRenderConfig {
      /** 是否启用打字机效果 */
      enableTypewriter?: boolean;
      /** 打字机速度（字符/秒） */
      typewriterSpeed?: number;
      /** 是否自动播放音频 */
      autoPlayAudio?: boolean;
      /** 是否显示实时转录 */
      showLiveTranscript?: boolean;
    }

    // ==================== UI层类型 ====================

    /**
     * UI状态接口
     */
    interface UIState {
      /** 是否显示连接状态 */
      showConnectionStatus: boolean;
      /** 是否显示音量指示器 */
      showVolumeIndicator: boolean;
      /** 是否显示打字机效果 */
      showTypewriter: boolean;
      /** 是否自动滚动到底部 */
      autoScrollToBottom: boolean;
    }

    /**
     * 用户操作接口
     */
    interface UserActions {
      /** 发送文本消息 */
      sendTextMessage: (text: string) => Promise<void>;
      /** 切换录音状态 */
      toggleRecording: () => void;
      /** 切换静音状态 */
      toggleMuted: () => void;
      /** 切换对话模式 */
      switchMode: (mode: ChatMode) => void;
      /** 打断AI回复 */
      interruptAiReply: () => Promise<void>;
      /** 重新连接 */
      reconnect: () => void;
    }

    // ==================== 提供商抽象接口 ====================

    /**
     * 大模型提供商接口
     */
    interface Provider {
      /** 提供商名称 */
      name: string;
      /** 支持的交互类型 */
      supportedInteractions: InteractionType[];
      /** 连接到服务 */
      connect: (config: ConnectionConfig) => Promise<void>;
      /** 断开连接 */
      disconnect: () => Promise<void>;
      /** 发送文本消息 */
      sendTextMessage: (text: string) => Promise<void>;
      /** 发送音频数据 */
      sendAudioData: (audioData: string) => Promise<void>;
      /** 清空音频缓冲区 */
      clearAudioBuffer: () => Promise<void>;
      /** 注册事件监听器 */
      on: (event: string, callback: (...args: unknown[]) => void) => void;
      /** 移除事件监听器 */
      off: (event: string, callback: (...args: unknown[]) => void) => void;
    }

    // ==================== 工具调用类型 ====================

    /**
     * 工具调用事件
     */
    interface ToolCallEvents {
      /** 工具调用开始 */
      onToolCallStart?: (toolCallId: string) => void;
      /** 工具调用完成 */
      onToolCallComplete?: (toolCallId: string) => void;
      /** 工具调用错误 */
      onToolCallError?: (toolCallId: string, error: Error) => void;
    }

    // ==================== 自定义事件类型 ====================

    /**
     * 自定义业务事件
     */
    interface CustomEvents {
      /** 应用跳转事件 */
      onAppNavigation?: (serviceCode: string) => void;
      /** 页面状态变化 */
      onPageStateChange?: (isActive: boolean) => void;
      /** 其他自定义事件 */
      onCustomEvent?: (eventName: string, data: unknown) => void;
    }

    // ==================== 综合配置接口 ====================

    /**
     * 对话系统完整配置
     */
    interface ConversationConfig {
      /** 连接配置 */
      connection: ConnectionConfig;
      /** 响应渲染配置 */
      rendering?: ResponseRenderConfig;
      /** UI配置 */
      ui?: Partial<UIState>;
      /** 支持的交互类型 */
      supportedInteractions?: InteractionType[];
      /** 默认对话模式 */
      defaultMode?: ChatMode;
    }

    /**
     * 对话系统事件集合
     */
    interface ConversationEvents extends 
      ConnectionEvents,
      VoiceInteractionEvents,
      TextInteractionEvents,
      ToolCallEvents,
      CustomEvents {
    }
  }
}

export {};

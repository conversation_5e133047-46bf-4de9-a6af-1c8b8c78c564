/**
 * 消息管理器
 * 负责管理对话消息的状态、增删改查等业务逻辑
 */

import { ref, type Ref } from 'vue';
import { nanoid } from 'nanoid';
import { MessageStatus } from '@/consts/message-status';
import { RoleType } from '@/consts/role-type';
import { MessageType } from '@/consts/message-type';

/**
 * 消息管理器实现类
 */
export class MessageManager implements Conversation.MessageManager {
  /** 消息列表 */
  public readonly messages: Ref<Conversation.Message[]> = ref([]);
  
  /** 当前AI回复消息 */
  private currentAiMessage: Conversation.Message | null = null;

  /**
   * 添加用户消息
   */
  addUserMessage(
    content: string, 
    messageId?: string, 
    status: MessageStatus = MessageStatus.COMPLETE
  ): Conversation.Message {
    const message: Conversation.Message = {
      id: messageId || nanoid(),
      role: RoleType.USER,
      content,
      createdAt: Date.now(),
      status,
      interactionType: Conversation.InteractionType.TEXT, // 默认为文本，可以后续更新
    };

    this.messages.value.push(message);
    return message;
  }

  /**
   * 添加AI消息
   */
  addAiMessage(messageId: string, previousMessageId?: string): Conversation.Message {
    const message: Conversation.Message = {
      id: messageId,
      role: RoleType.ASSISTANT,
      content: '',
      createdAt: Date.now(),
      status: MessageStatus.INCOMPLETE,
      previousMessageId,
      type: MessageType.TEXT, // 默认为文本类型
    };

    this.messages.value.push(message);
    this.currentAiMessage = message;
    return message;
  }

  /**
   * 添加工具调用消息
   */
  addUtilMessage(messageId: string): Conversation.Message {
    const message: Conversation.Message = {
      id: messageId,
      role: RoleType.ASSISTANT,
      content: '',
      createdAt: Date.now(),
      status: MessageStatus.LOADING,
      isUtilCall: true,
      type: MessageType.TEXT,
    };

    this.messages.value.push(message);
    this.currentAiMessage = message;
    return message;
  }

  /**
   * 更新消息内容
   */
  updateMessageContent(messageId: string, content: string): void {
    const message = this.findMessageById(messageId);
    if (message) {
      message.content = content;
    }
  }

  /**
   * 追加消息内容
   */
  appendMessageContent(messageId: string, content: string): void {
    const message = this.findMessageById(messageId);
    if (message) {
      message.content += content;
    }
  }

  /**
   * 更新消息状态
   */
  updateMessageStatus(messageId: string, status: MessageStatus): void {
    const message = this.findMessageById(messageId);
    if (message) {
      message.status = status;
    }
  }

  /**
   * 删除消息
   */
  removeMessage(messageId: string): void {
    const index = this.messages.value.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      this.messages.value.splice(index, 1);

      // 如果删除的是当前AI消息，清空引用
      if (this.currentAiMessage?.id === messageId) {
        this.currentAiMessage = null;
      }
    }
  }

  /**
   * 根据ID查找消息
   */
  findMessageById(messageId: string): Conversation.Message | undefined {
    return this.messages.value.find(msg => msg.id === messageId);
  }

  /**
   * 获取当前AI回复消息
   */
  getCurrentAiMessage(): Conversation.Message | null {
    return this.currentAiMessage;
  }

  /**
   * 设置当前AI回复消息
   */
  setCurrentAiMessage(message: Conversation.Message): void {
    this.currentAiMessage = message;
  }

  /**
   * 清空当前AI回复消息引用
   */
  clearCurrentAiMessage(): void {
    this.currentAiMessage = null;
  }

  /**
   * 检查是否有当前AI回复消息
   */
  hasCurrentAiMessage(): boolean {
    return this.currentAiMessage !== null;
  }

  /**
   * 清理空消息
   */
  clearEmptyMessages(): void {
    this.messages.value = this.messages.value.filter(msg => {
      // 保留有内容的消息，或者状态为加载中的消息
      return msg.content.trim() !== '' || msg.status === MessageStatus.LOADING;
    });
  }

  /**
   * 获取最后一条消息
   */
  getLastMessage(): Conversation.Message | undefined {
    return this.messages.value[this.messages.value.length - 1];
  }

  /**
   * 获取指定角色的最后一条消息
   */
  getLastMessageByRole(role: RoleType): Conversation.Message | undefined {
    for (let i = this.messages.value.length - 1; i >= 0; i--) {
      const message = this.messages.value[i];
      if (message.role === role) {
        return message;
      }
    }
    return undefined;
  }

  /**
   * 获取消息总数
   */
  getMessageCount(): number {
    return this.messages.value.length;
  }

  /**
   * 获取指定状态的消息数量
   */
  getMessageCountByStatus(status: MessageStatus): number {
    return this.messages.value.filter(msg => msg.status === status).length;
  }

  /**
   * 批量更新消息
   */
  batchUpdateMessages(updates: Array<{ id: string; updates: Partial<Conversation.Message> }>): void {
    updates.forEach(({ id, updates: messageUpdates }) => {
      const message = this.findMessageById(id);
      if (message) {
        Object.assign(message, messageUpdates);
      }
    });
  }

  /**
   * 插入历史消息（用于历史记录加载）
   */
  insertHistoryMessages(messages: Conversation.Message[]): void {
    this.messages.value.unshift(...messages);
  }

  /**
   * 清空所有消息
   */
  clearAllMessages(): void {
    this.messages.value = [];
    this.currentAiMessage = null;
  }

  /**
   * 获取消息统计信息
   */
  getMessageStats(): {
    total: number;
    byRole: Record<RoleType, number>;
    byStatus: Record<MessageStatus, number>;
    byType: Record<MessageType, number>;
  } {
    const stats = {
      total: this.messages.value.length,
      byRole: {} as Record<RoleType, number>,
      byStatus: {} as Record<MessageStatus, number>,
      byType: {} as Record<MessageType, number>,
    };

    // 初始化计数器
    Object.values(RoleType).forEach(role => {
      stats.byRole[role] = 0;
    });
    Object.values(MessageStatus).forEach(status => {
      stats.byStatus[status] = 0;
    });
    Object.values(MessageType).forEach(type => {
      stats.byType[type] = 0;
    });

    // 统计消息
    this.messages.value.forEach(message => {
      stats.byRole[message.role]++;
      stats.byStatus[message.status]++;
      if (message.type) {
        stats.byType[message.type]++;
      }
    });

    return stats;
  }

  /**
   * 验证消息完整性
   */
  validateMessages(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    this.messages.value.forEach((message, index) => {
      // 检查必需字段
      if (!message.id) {
        errors.push(`Message at index ${index} missing id`);
      }
      if (!message.role) {
        errors.push(`Message at index ${index} missing role`);
      }
      if (typeof message.content !== 'string') {
        errors.push(`Message at index ${index} has invalid content type`);
      }
      if (!message.createdAt) {
        errors.push(`Message at index ${index} missing createdAt`);
      }
      if (!message.status) {
        errors.push(`Message at index ${index} missing status`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * 创建消息管理器实例
 */
export function createMessageManager(): MessageManager {
  return new MessageManager();
}

import { reactive } from 'vue';
import { Auth } from '@/config/auth';
import { authService } from '@/services/auth';


interface IAuthData {
  auths: string[] | null;
}

type GetAuthCodeFunction = (auth: typeof Auth) => string;

export class UseAuth {
  private ob;

  private promise: Promise<void> | undefined;

  constructor() {
    this.ob = reactive<IAuthData>({
      auths: null,
    });
    this.promise = undefined;
  }

  async loadAuths(): Promise<void> {
    const data = await authService.getAuths();
    this.ob.auths = data;
  }

  async loadAuthsIfNeeded() {
    if (this.ob.auths !== null) {
      return;
    }

    if (this.promise !== undefined) {
      await this.promise;
      return;
    }

    this.promise = this.loadAuths();
    await this.promise;
  }

  hasAuths(): boolean {
    return this.ob.auths !== null;
  }

  can(authCodeOrFn: GetAuthCodeFunction | string): boolean {
    if (typeof authCodeOrFn === 'string') {
      return new Set(this.ob.auths).has(authCodeOrFn);
    }

    const p = authCodeOrFn(Auth);
    return new Set(this.ob.auths).has(p);
  }
}


let instance: UseAuth | undefined;

export function useAuth() {
  if (instance === undefined) {
    instance = new UseAuth();
  }

  return instance;
}

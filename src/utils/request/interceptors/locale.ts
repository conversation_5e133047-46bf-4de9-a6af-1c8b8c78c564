import type { InternalAxiosRequestConfig } from 'axios';
import { Locale } from '@/config/locale';


/**
 * 请求拦截器，用于在每个axios请求中添加语言参数。
 *
 * 该函数接收一个axios的请求配置对象作为参数，返回一个新的请求配置对象。
 * 主要作用是根据当前系统的语言设置，动态添加lang参数到请求的params中，
 * 以便后端可以根据lang参数返回对应语言的接口响应。
 *
 * @param config axios的请求配置对象，包含请求的参数、头部等信息。
 * @returns 返回一个新的请求配置对象，其中添加了lang参数。
 */
export function interceptorLocale(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig {
  // 根据系统语言判断使用的语言版本
  const lang = window.native.i18n.language === 'en' ? Locale.EN_US : Locale.ZH_MO;

  // 合并原始请求参数和新的lang参数
  const params = {
    ...config.params,
    lang,
  };

  // 将新的参数对象赋值给config，更新请求配置
  Object.assign(config, { params });

  // 返回更新后的请求配置
  return config;
}

<script setup lang="ts">
import Input from '../input';
import Speech from '../speech';

import { ChatMode } from '@/consts/chat-mode';

interface FooterProps {
  currentMode: ChatMode;
  isRecording: boolean;
  isAiReplying: boolean;
  isConnecting: boolean;
  isConnectError: boolean;
  volume: number;
}

defineProps<FooterProps>();

const inputValue = defineModel<string>('input-value', {
  default: '',
});

const emits = defineEmits<{
  'on-switch-mode': [mode: ChatMode];
  'on-send-message': [message: string];
  'on-stop-message': [];
  'on-toggle-recording': [];
  'on-interrupt-ai-replying': [];
}>();

function onSendMessage(message: string) {
  emits('on-send-message', message);
}

function onSwitchMode(mode: ChatMode) {
  emits('on-switch-mode', mode);
}

function onToggleRecording() {
  emits('on-toggle-recording');
}

function onInterruptAiReply() {
  emits('on-interrupt-ai-replying');
}

</script>


<template>
  <div class="chat-footer">
    <TransitionFade>
      <Speech
        v-if="currentMode === ChatMode.SPEECH"
        :is-connecting="isConnecting"
        :is-connect-error="isConnectError"
        :is-recording="isRecording"
        :is-ai-replying="isAiReplying"
        :volume="volume"
        @on-switch-mode="onSwitchMode"
        @on-toggle-recording="onToggleRecording"
        @on-interrupt-ai-replying="onInterruptAiReply"
      />

      <Input
        v-else
        v-model="inputValue"
        :sending="isAiReplying"
        @on-switch-mode="onSwitchMode"
        @on-send="onSendMessage"
        @on-interrupt-ai-replying="onInterruptAiReply"
      />
    </TransitionFade>
  </div>
</template>


<style scoped lang="less">
.chat-footer {
  min-height: 98px;
}

:deep(.fade-transition-enter-from) {
  transform: translateY(20px);
  opacity: 0;
}

:deep(.fade-transition-leave-to) {
  transform: translateY(-20px);
  opacity: 0;
}

:deep(.fade-transition-enter-active) {
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

:deep(.fade-transition-leave-active) {
  transition: opacity 0.2s ease-in, transform 0.2s ease-in;
}
</style>

/**
 * 文本交互实现
 * 处理文本输入、发送、AI文本回复等文本相关功能
 */

import { ref, type Ref } from 'vue';
import { BaseInteraction, InteractionState } from './base-interaction';
import type { BaseConnection } from '../connection/base-connection';

/**
 * 文本交互实现类
 */
export class TextInteraction extends BaseInteraction {
  /** 当前输入文本 */
  public readonly inputText: Ref<string> = ref('');
  
  /** 是否正在发送 */
  public readonly isSending: Ref<boolean> = ref(false);

  constructor(connection: BaseConnection) {
    super(connection, Conversation.InteractionType.TEXT);
  }

  /**
   * 初始化文本交互
   */
  async initialize(): Promise<void> {
    try {
      // 设置连接事件监听器
      this.setupTextConnectionListeners();
      
      this.setState(InteractionState.IDLE);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 启动文本交互
   */
  async start(): Promise<void> {
    try {
      this.setState(InteractionState.ACTIVE);
      this.emit('textInteractionStarted');
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 停止文本交互
   */
  async stop(): Promise<void> {
    try {
      this.isSending.value = false;
      this.setState(InteractionState.IDLE);
      this.emit('textInteractionStopped');
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 重置文本交互状态
   */
  reset(): void {
    this.inputText.value = '';
    this.isSending.value = false;
    this.setState(InteractionState.IDLE);
  }

  /**
   * 发送文本消息
   */
  async sendMessage(text?: string): Promise<void> {
    const messageText = text || this.inputText.value.trim();
    
    if (!messageText) {
      throw new Error('Message text is empty');
    }

    if (this.isSending.value) {
      throw new Error('Already sending a message');
    }

    if (!this.connection.isConnected()) {
      throw new Error('Connection not established');
    }

    try {
      this.isSending.value = true;
      this.setState(InteractionState.PROCESSING);

      // 发送消息到连接
      if ('sendTextMessage' in this.connection) {
        const success = await (this.connection as any).sendTextMessage(messageText);
        if (!success) {
          throw new Error('Failed to send message');
        }
      } else {
        throw new Error('Connection does not support text messages');
      }

      // 清空输入文本（如果使用的是当前输入）
      if (!text) {
        this.inputText.value = '';
      }

      this.emit('messageSent', messageText);
      
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    } finally {
      this.isSending.value = false;
      this.setState(InteractionState.ACTIVE);
    }
  }

  /**
   * 更新输入文本
   */
  updateInputText(text: string): void {
    this.inputText.value = text;
    this.emit('inputTextChanged', text);
  }

  /**
   * 清空输入文本
   */
  clearInputText(): void {
    this.inputText.value = '';
    this.emit('inputTextCleared');
  }

  /**
   * 获取当前输入文本
   */
  getInputText(): string {
    return this.inputText.value;
  }

  /**
   * 检查是否可以发送消息
   */
  canSendMessage(): boolean {
    return (
      this.isActive() &&
      !this.isSending.value &&
      this.connection.isConnected() &&
      this.inputText.value.trim().length > 0
    );
  }

  /**
   * 设置文本连接事件监听器
   */
  private setupTextConnectionListeners(): void {
    // AI文本回复开始
    this.connection.on('conversationItemCreated', (message: unknown) => {
      // 根据消息类型判断是否为AI文本回复
      if (this.isAiTextMessage(message)) {
        this.emit('aiTextStart', this.extractMessageId(message));
      }
    });

    // AI文本增量
    this.connection.on('aiTextDelta', (text: string, messageId: string) => {
      this.emit('aiTextDelta', text, messageId);
    });

    // 响应完成
    this.connection.on('responseDone', (message: unknown) => {
      this.emit('aiTextComplete', this.extractMessageId(message));
    });
  }

  /**
   * 判断是否为AI文本消息
   */
  private isAiTextMessage(message: unknown): boolean {
    // 这里需要根据具体的消息格式来判断
    // 暂时返回true，具体实现需要根据实际消息结构调整
    return typeof message === 'object' && message !== null;
  }

  /**
   * 提取消息ID
   */
  private extractMessageId(message: unknown): string {
    // 这里需要根据具体的消息格式来提取ID
    // 暂时返回空字符串，具体实现需要根据实际消息结构调整
    if (typeof message === 'object' && message !== null && 'id' in message) {
      return String((message as any).id);
    }
    return '';
  }

  /**
   * 连接建立时的处理
   */
  protected onConnectionEstablished(): void {
    // 文本交互在连接建立后可以立即使用
    if (this.getState() === InteractionState.IDLE) {
      this.setState(InteractionState.ACTIVE);
    }
  }

  /**
   * 连接丢失时的处理
   */
  protected onConnectionLost(): void {
    super.onConnectionLost();
    
    // 停止发送状态
    this.isSending.value = false;
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    super.cleanup();
    
    this.inputText.value = '';
    this.isSending.value = false;
  }
}

/**
 * 文本交互工厂函数
 */
export function createTextInteraction(connection: BaseConnection): TextInteraction {
  return new TextInteraction(connection);
}

/**
 * 文本交互配置接口
 */
export interface TextInteractionConfig {
  /** 最大输入长度 */
  maxInputLength?: number;
  /** 是否自动清空输入 */
  autoClearInput?: boolean;
  /** 发送超时时间 */
  sendTimeout?: number;
  /** 是否启用输入验证 */
  enableInputValidation?: boolean;
}

/**
 * 增强的文本交互类
 * 包含更多配置选项和功能
 */
export class EnhancedTextInteraction extends TextInteraction {
  private config: TextInteractionConfig;

  constructor(connection: BaseConnection, config: TextInteractionConfig = {}) {
    super(connection);
    this.config = {
      maxInputLength: 1000,
      autoClearInput: true,
      sendTimeout: 30000,
      enableInputValidation: true,
      ...config,
    };
  }

  /**
   * 验证输入文本
   */
  private validateInput(text: string): { valid: boolean; error?: string } {
    if (!this.config.enableInputValidation) {
      return { valid: true };
    }

    if (!text.trim()) {
      return { valid: false, error: 'Message cannot be empty' };
    }

    if (this.config.maxInputLength && text.length > this.config.maxInputLength) {
      return { 
        valid: false, 
        error: `Message too long (max ${this.config.maxInputLength} characters)` 
      };
    }

    return { valid: true };
  }

  /**
   * 重写发送消息方法，添加验证和超时控制
   */
  async sendMessage(text?: string): Promise<void> {
    const messageText = text || this.inputText.value.trim();
    
    // 验证输入
    const validation = this.validateInput(messageText);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // 设置超时
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Send message timeout'));
      }, this.config.sendTimeout);
    });

    await Promise.race([
      super.sendMessage(messageText),
      timeoutPromise,
    ]);
  }

  /**
   * 重写更新输入文本方法，添加长度限制
   */
  updateInputText(text: string): void {
    let limitedText = text;
    
    if (this.config.maxInputLength && text.length > this.config.maxInputLength) {
      limitedText = text.substring(0, this.config.maxInputLength);
    }
    
    super.updateInputText(limitedText);
  }
}

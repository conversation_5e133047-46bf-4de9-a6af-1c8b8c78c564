interface Parameters {
  onCompleted?: () => void;
}

export function useAudioPlayer({
  onCompleted,
}: Parameters = {}) {
  let isPlaying = false;

  function reset() {
  }

  function play(base64Audio: string, isFinish: boolean = false) {
    isPlaying = true;

    native.audio.startPlay({
      data: base64Audio,
      isFinish,
      onCompleted: () => {
        isPlaying = false;
        onCompleted?.();
      },
    });
  }

  function stop() {
    isPlaying = false;
    native.audio.stopPlay();
  }

  function getIsPlaying() {
    return isPlaying;
  }

  return {
    play,
    stop,
    reset,
    getIsPlaying,
  };
}

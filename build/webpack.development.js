const path = require('path');
const { DefinePlugin } = require("webpack");
const { merge } = require('webpack-merge');
const ESLintPlugin = require('eslint-webpack-plugin');
const StylelintWebpackPlugin = require('stylelint-webpack-plugin');
const getBaseConfig = require('./webpack.base');

const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = (env) => {
  const config = merge(getBaseConfig(env), {
    entry: {
      app: resolve('../src/app.ts'),
    },
    mode: 'development',
    plugins: [
      new DefinePlugin({
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_DEVTOOLS__: false,
      }),
      new ESLintPlugin({
        extensions: ['js', 'ts', 'vue'],
      }),
      new StylelintWebpackPlugin({
        files: ['**/*.{vue,css,less}'],
      }),
    ],
    devServer: {
      static: {
        directory: resolve('../src'),
      },
      compress: true,
      client: {
        overlay: false, // 禁用错误遮罩层
      },
    },
  });

  return config;
};

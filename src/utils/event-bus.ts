// 导入mitt库，用于创建一个事件总线，以实现组件间轻量级的通信
import mitt, { Emitter } from 'mitt';

// 创建一个mitt实例，传入MittEvents类型以增强类型安全
const emitter: Emitter<MittEvents> = mitt<MittEvents>();

/**
 * 注册监听事件。
 * @param event 事件名称
 * @param fn 事件触发时执行的回调函数
 * @returns 返回一个函数，用于移除此事件监听器
 */
function on(event: keyof MittEvents, fn: (payload: MittEvents[keyof MittEvents]) => void) {
  emitter.on(event, fn);
  return () => emitter.off(event, fn);
}

// 导出事件总线对象，包含on、emit、clearAll方法
export default {
  on,
  emit: emitter.emit,
  clear: emitter.all.clear,
};

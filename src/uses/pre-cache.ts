/* eslint-disable no-param-reassign */
// 定义事件回调函数类型
type EventCallback = () => void;

// 定义缓存实例接口
interface CacheInstance {
  list: EventCallback[];
  timeoutId: NodeJS.Timeout | null;
  intervalId: NodeJS.Timeout | null;
  isFinish: boolean;
  execFull: boolean;
  completeEvent: EventCallback | null;
}

// 定义缓存实例操作接口
interface CacheInstanceMethods {
  addEvent: (event: EventCallback) => void;
  clear: () => void;
  start: () => void;
  markComplete: (event: EventCallback) => void;
  stop: () => void;
  setExecFull: (full: boolean) => void;
}

export function usePreCache() {
  // 存储所有缓存实例的Map
  const instances = new Map<string, CacheInstance>();
  const preTimeout = 2000;
  const interval = 0;

  // 创建新的缓存实例
  function createInstance(): CacheInstance {
    return {
      list: [],
      timeoutId: null,
      intervalId: null,
      isFinish: false,
      execFull: false,
      completeEvent: null,
    };
  }

  // 获取或创建指定item_id的缓存实例
  function getInstance(itemId: string): CacheInstance {
    if (!instances.has(itemId)) {
      instances.set(itemId, createInstance());
    }
    return instances.get(itemId)!;
  }

  // 为指定实例启动处理队列
  function startInstance(itemId: string) {
    const instance = getInstance(itemId);
    if (instance.intervalId) {
      return; // 已经在运行中
    }

    const intervalId = setInterval(() => {
      if (instance.list.length > 0) {
        const event = instance.list.shift();
        if (event) {
          event();
        }
      } else if (instance.isFinish && instance.list.length === 0) {
        if (instance.intervalId) {
          clearInterval(instance.intervalId);
          instance.intervalId = null;
          instance.completeEvent?.();
        }
      }
    }, interval);

    instance.intervalId = intervalId;
  }

  // 获取指定item_id的缓存操作方法
  function getInstanceMethods(itemId: string): CacheInstanceMethods {
    const instance = getInstance(itemId);

    return {
      addEvent: (event: EventCallback) => {
        instance.list.push(event);

        if (!instance.timeoutId) {
          instance.timeoutId = setTimeout(() => {
            instance.timeoutId = null;
            startInstance(itemId);
          }, preTimeout);
        }
      },

      clear: () => {
        instance.list.length = 0;
      },

      start: () => {
        startInstance(itemId);
      },

      markComplete: (event: EventCallback) => {
        instance.isFinish = true;
        instance.completeEvent = event;
      },

      setExecFull: (full: boolean) => {
        instance.execFull = full;
      },

      stop: () => {
        instance.isFinish = false;

        if (instance.execFull) {
          instance.list.forEach((event) => event());
        }

        // 清理定时器
        if (instance.timeoutId) {
          clearTimeout(instance.timeoutId);
          instance.timeoutId = null;
        }
        if (instance.intervalId) {
          clearInterval(instance.intervalId);
          instance.intervalId = null;
        }

        // 清空队列
        instance.list.length = 0;
      },
    };
  }

  // 清理指定item_id的缓存实例
  function clearInstance(itemId: string) {
    const instance = instances.get(itemId);
    if (instance) {
      // 停止所有定时器
      if (instance.timeoutId) {
        clearTimeout(instance.timeoutId);
      }
      if (instance.intervalId) {
        clearInterval(instance.intervalId);
      }
      // 从Map中移除
      instances.delete(itemId);
    }
  }

  // 清理所有缓存实例
  function clearAllInstances() {
    instances.forEach((instance) => {
      if (instance.timeoutId) {
        clearTimeout(instance.timeoutId);
      }
      if (instance.intervalId) {
        clearInterval(instance.intervalId);
      }
    });
    instances.clear();
  }

  // 获取当前活跃的实例数量
  function getActiveInstancesCount(): number {
    return instances.size;
  }

  // 获取指定实例的状态信息
  function getInstanceStatus(itemId: string) {
    const instance = instances.get(itemId);
    if (!instance) {
      return null;
    }

    return {
      queueLength: instance.list.length,
      isFinish: instance.isFinish,
      hasTimeout: instance.timeoutId !== null,
      isRunning: instance.intervalId !== null,
    };
  }

  function stopAll() {
    instances.forEach((instance) => {
      instance.isFinish = false;

      if (instance.execFull) {
        instance.list.forEach((event) => event());
      }

      // 清理定时器
      if (instance.timeoutId) {
        clearTimeout(instance.timeoutId);
        instance.timeoutId = null;
      }
      if (instance.intervalId) {
        clearInterval(instance.intervalId);
        instance.intervalId = null;
      }

      // 清空队列
      instance.list.length = 0;
    });
  }

  return {
    // 获取指定item_id的缓存操作方法
    getCache: getInstanceMethods,
    // 实例管理方法
    clearInstance,
    clearAllInstances,
    getActiveInstancesCount,
    getInstanceStatus,
    stopAll,
  };
}

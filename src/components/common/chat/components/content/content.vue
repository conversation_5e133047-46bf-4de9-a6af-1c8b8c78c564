<script setup lang="ts">
import { useTemplateRef, watch, nextTick, computed } from 'vue';
import { PullRefresh } from 'vant';
import { useI18n } from 'vue-i18n';

import Welcome from '../welcome';
import Messages from '../messages';

import { Align } from '@/consts/align';
import { useAutoScroll } from '../../uses/auto-scroll';

withDefaults(
  defineProps<{
    messages?: Chat.Message[];
    align?: Align;
  }>(),
  {
    align: Align.LEFT_RIGHT,
    messages: () => [],
  },
);

const emits = defineEmits<{
  'on-load-history': [];
}>();

const historyLoading = defineModel<boolean>('historyLoading', {
  default: false,
});
// 滚动容器引用
const contentInnerRef = useTemplateRef<HTMLElement>('contentInnerRef');
const { t } = useI18n();

// 是否应该自动滚动（用户向上滚动后会设为false）
const autoScroll = useAutoScroll(contentInnerRef);

// 计算是否应该允许Messages组件自动滚动
// 在加载历史记录时禁止自动滚动，避免干扰滚动位置恢复
const shouldAutoScroll = computed(() => {
  return autoScroll.autoScroll.value && !historyLoading.value;
});

// 监听历史记录加载状态变化
watch(historyLoading, (newLoading, oldLoading) => {
  if (!oldLoading && newLoading) {
    // 开始加载历史记录，保存当前滚动状态
    autoScroll.saveScrollStateBeforeHistory();
  } else if (oldLoading && !newLoading) {
    // 历史记录加载完成，恢复滚动位置
    nextTick(() => {
      autoScroll.restoreScrollStateAfterHistory();
    });
  }
});

function onLoadHistory() {
  emits('on-load-history');
}

</script>


<template>
  <div class="chat-content">
    <PullRefresh
      v-model="historyLoading"
      class="chat-pull-refresh"
      :loosing-text="t('chat.content.messages.loosingText')"
      :loading-text="t('chat.content.messages.loadingText')"
      :disabled="!autoScroll.isAtTop.value"
      @refresh="onLoadHistory"
    >
      <div
        ref="contentInnerRef"
        class="chat-content-inner"
        :class="{
          'chat-content-inner-history-loading': historyLoading,
        }"
      >
        <Messages
          v-if="messages.length"
          :messages="messages"
          :align="align"
          :auto-scroll="shouldAutoScroll"
        />

        <Welcome v-else />
      </div>
    </PullRefresh>
  </div>
</template>


<style scoped lang="less">
.chat-content {
  height: 100%;

  .chat-pull-refresh {
    height: 100%;
  }

  .chat-content-inner {
    height: 100%;
    overflow: hidden auto;

    &.chat-content-inner-history-loading {
      height: calc(100% - 50px);
    }
  }
}
</style>

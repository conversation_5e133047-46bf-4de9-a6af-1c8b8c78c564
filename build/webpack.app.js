const path = require('path');

const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = () => {
  return {
    target: ['web', 'es5'],
    output: {
      path: resolve('../dist'), // 出口文件路径
      filename: 'app.js',
      library: {
        name: 'app',
        type: 'umd',
      },
    },
    entry: resolve('../src/app.ts'),
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: [
            'thread-loader',
            {
              loader: 'esbuild-loader',
              options: {
                loader: 'js',
                target: 'es2015', // 设置编译目标，默认是 esnext
              },
            },
          ],
        },
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: [
            'thread-loader',
            {
              loader: 'esbuild-loader',
              options: {
                loader: 'ts', // 使用 TypeScript loader
                target: 'es2015',
              },
            },
          ],
        },
      ],
    },
  };
}
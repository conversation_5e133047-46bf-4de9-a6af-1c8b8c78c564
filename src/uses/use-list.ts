import { toRefs, reactive, toRaw, UnwrapNestedRefs, UnwrapRef } from 'vue';


interface UseListConfig<T> {
  fetch: (params: unknown) => Promise<Pick<State<T>, 'data' | 'total'>>
  size?: number
}

interface State<T> {
  loading: boolean; // 数据加载中标志
  refreshing: boolean; // 正在刷新标志
  finished: boolean; // 是否已加载完所有数据
  error: boolean; // 加载数据时是否发生错误
  page: { size: number; index: number }; // 当前页信息
  data: T[]; // 数据列表
  offset: number; // 数据偏移量
  total: number; // 总数据条数
}

export const useList = <T>(config: UseListConfig<T>) => {
  if (typeof config !== 'object' || !('fetch' in config) || typeof config.fetch !== 'function') {
    throw new Error('useList must have "fetch" function');
  }

  function createPage({ size }: Pick<UseListConfig<T>, 'size'>) {
    return {
      size: (size ?? 20),
      index: 1,
    };
  }

  const state = reactive<State<T>>({
    loading: false,
    refreshing: false,
    finished: false,
    error: false,
    page: createPage({ size: config.size }),
    data: [],
    offset: 0,
    total: 0,
  }) as UnwrapNestedRefs<State<T>>;

  async function fetch(): Promise<Pick<State<T>, 'data' | 'total'>> {
    const res = await config.fetch({
      page: toRaw(state.page),
    });
    return res;
  }

  function insertData({ data, total }: Pick<State<T>, 'data' | 'total'>, append = false) {
    if (append) {
      state.data = (Array.isArray(data) ? [...state.data, ...data] : []) as UnwrapRef<T[]>;
    } else {
      state.data = (Array.isArray(data) ? data : []) as UnwrapRef<T[]>;
    }

    state.total = total || 0;
    state.page.index += 1;
    state.offset = (state.page.index - 1) * state.page.size;
    state.finished = state.data.length >= state.total;

    if (state.error) {
      state.error = false;
    }
  }

  async function loadData() {
    state.loading = true;
    try {
      const res = await fetch();
      insertData(res, true);
    } catch (error) {
      state.error = true;
      throw error;
    } finally {
      state.loading = false;
    }
  }

  async function refreshData() {
    state.refreshing = true;
    state.page.index = 1;
    try {
      const res = await fetch();
      insertData(res, false);
    } catch (error) {
      state.error = true;
      throw error;
    } finally {
      state.refreshing = false;
    }
  }

  function removeItem(index: number) {
    state.data.splice(index, 1);
  }

  function reset() {
    state.loading = false;
    state.refreshing = false;
    state.finished = false;
    state.error = false;
    state.page.index = 1;
    state.data = [];
    state.offset = 0;
    state.total = 0;
  }

  return {
    ...toRefs(state),
    load: loadData,
    refresh: refreshData,
    removeItem,
    reset,
  };
};

export default useList;

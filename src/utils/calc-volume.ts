/**
 * 音量计算配置接口
 */
export interface VolumeConfig {
  /** 最小音量阈值 */
  minThreshold?: number;
  /** 最大音量阈值 */
  maxThreshold?: number;
  /** 音量增益倍数 */
  volumeGain?: number;
}

/**
 * 音量计算结果接口
 */
export interface VolumeResult {
  /** 当前音量值 (0-100) */
  volume: number;
  /** RMS 原始值 */
  rms: number;
  /** 是否检测到声音 */
  hasSound: boolean;
}

/**
 * 从 base64 编码的音频数据计算音量
 *
 * @param base64Audio - base64 编码的音频数据
 * @param config - 音量计算配置
 * @returns 音量值 (0-100)
 */
export function calcVolume(base64Audio: string, config: VolumeConfig = {}): number {
  try {
    // 参数验证
    if (!base64Audio || typeof base64Audio !== 'string') {
      return 0;
    }

    // 解码 base64 数据
    const binary = atob(base64Audio);
    if (binary.length === 0) {
      return 0;
    }

    // 转换为字节数组
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i += 1) {
      bytes[i] = binary.charCodeAt(i);
    }

    // 确保字节长度是偶数（Int16 需要2个字节）
    const validLength = Math.floor(bytes.length / 2) * 2;
    if (validLength === 0) {
      return 0;
    }

    // 转换为 Int16Array (PCM 16-bit 数据)
    const pcmData = new Int16Array(bytes.buffer, 0, validLength / 2);

    // 使用改进的音量计算
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    return calculateVolumeFromPCM(pcmData, config);
    // eslint-disable-next-line no-unused-vars
  } catch (error) {
    return 0;
  }
}

/**
 * 从 PCM 数据计算音量（内部函数）
 */
function calculateVolumeFromPCM(pcmData: Int16Array, config: VolumeConfig = {}): number {
  if (pcmData.length === 0) {
    return 0;
  }

  // 默认配置
  const defaultConfig = {
    minThreshold: 100, // 降低最小阈值，提高灵敏度
    maxThreshold: 32767, // Int16 最大值
    volumeGain: 3.0, // 增加增益，让音量更明显
    ...config,
  };

  // 计算 RMS (Root Mean Square)
  let sum = 0;
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (let i = 0; i < pcmData.length; i += 1) {
    const sample = Math.abs(pcmData[i]); // 取绝对值
    sum += sample * sample;
  }

  const rms = Math.sqrt(sum / pcmData.length);

  // 如果 RMS 值太小，直接返回 0
  if (rms < defaultConfig.minThreshold) {
    return 0;
  }

  // 将 RMS 值映射到 0-100 范围
  const normalizedVolume = Math.min(
    100,
    Math.max(0, ((rms - defaultConfig.minThreshold) / (defaultConfig.maxThreshold - defaultConfig.minThreshold)) * 100),
  );

  // 应用音量增益
  const gainedVolume = Math.min(100, normalizedVolume * defaultConfig.volumeGain);

  return Math.round(gainedVolume);
}

/**
 * 获取详细的音量信息（可选的扩展函数）
 */
export function getVolumeDetails(base64Audio: string, config: VolumeConfig = {}): VolumeResult {
  try {
    const binary = atob(base64Audio);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i += 1) {
      bytes[i] = binary.charCodeAt(i);
    }

    const validLength = Math.floor(bytes.length / 2) * 2;
    if (validLength === 0) {
      return { volume: 0, rms: 0, hasSound: false };
    }

    const pcmData = new Int16Array(bytes.buffer, 0, validLength / 2);

    // 计算 RMS
    let sum = 0;
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < pcmData.length; i += 1) {
      const sample = Math.abs(pcmData[i]);
      sum += sample * sample;
    }
    const rms = Math.sqrt(sum / pcmData.length);

    const volume = calculateVolumeFromPCM(pcmData, config);
    const hasSound = rms > (config.minThreshold ?? 100);

    return { volume, rms, hasSound };
    // eslint-disable-next-line no-unused-vars
  } catch (error) {
    return { volume: 0, rms: 0, hasSound: false };
  }
}

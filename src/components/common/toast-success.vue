<script setup lang="ts" name="ToastSuccess">
import { ref, onMounted } from 'vue';

const props = withDefaults(defineProps<{
  duration?: number
  title?: string
}>(), {
  duration: 0,
  title: undefined,
});

const model = ref(true);

type Emits = (e: 'hide') => void;

const emit = defineEmits<Emits>();

onMounted(() => {
  setTimeout(() => {
    model.value = false;
    emit('hide');
  }, props.duration);
});
</script>


<template>
  <VanPopup
    v-model:show="model"
    class="toast-success-popup"
  >
    <div class="toast-success-wrap">
      <div class="body">
        <img
          class="ico-success"
          src="@/assets/img/icon-success.png"
        >
        <div>{{ $props.title }}</div>
      </div>
    </div>
  </VanPopup>
</template>


<style lang="less">
.van-popup.toast-success-popup {
  background-color: transparent;
}

.toast-success-wrap {
  box-sizing: border-box;
  width: calc(100vw - var(--van-padding-md) * 2);
  padding: 0 38px;

  .body {
    padding: 46px 20px;
    color: #222;
    font-weight: 400;
    font-size: 17px;
    text-align: center;
    background-color: #fff;
    border-radius: 5px;

    .ico-success {
      width: 35px;
    }

    .title {
      margin-top: 10px;
    }
  }
}
</style>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import _ from 'lodash';

const props = defineProps<{
  text?: string;
}>();

const { t } = useI18n();
const text = computed(() => {
  return _.isNil(props.text) ? t('chat.content.welcome.text') : props.text;
});

</script>


<template>
  <div class="chat-welcome">
    <img
      class="logo"
      src="@/assets/img/chat/logo.png"
    >
    <div class="text">
      {{ text }}
    </div>
  </div>
</template>

<style scoped lang="less">
.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;

  .logo {
    width: 70px;
    height: 70px;
  }

  .text {
    margin-top: 8px;
    color: #666;
  }
}
</style>

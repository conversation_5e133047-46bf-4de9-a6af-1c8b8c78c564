<script setup lang="ts">
import { onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';

import IconVolume from '@/assets/img/icon-volume.png';
import IconVolumeMuted from '@/assets/img/icon-volume-muted.png';

defineProps<{
  isMuted: boolean;
  isShowMuted: boolean;
}>();

const { t } = useI18n();

function onGoBack() {
  native.navigator.dismiss();
}

const emits = defineEmits<{
  'on-toggle-muted': [],
}>();

function onToggleMuted() {
  emits('on-toggle-muted');
}

onBeforeMount(() => {
  native.navigator.setNavigationBarHidden(true, false);
});

</script>


<template>
  <div class="title-bar">
    <div class="action-wrap">
      <img
        src="@/assets/img/icon-back.png"
        @click="onGoBack()"
      >
    </div>

    <span class="title">{{ t('home.title') }}</span>

    <div class="action-wrap">
      <img
        v-if="isShowMuted"
        :src="isMuted ? IconVolumeMuted : IconVolume"
        @click="onToggleMuted()"
      >
    </div>
  </div>
</template>


<style scoped lang="less">
.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;

  .title {
    color: #111;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: -0.288px;
  }

  .action-wrap {
    width: 34px;
    height: 34px;

    > img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

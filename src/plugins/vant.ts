import { App } from 'vue';
import Vant, { Locale } from 'vant';
import type { Composer } from 'vue-i18n';
import enUS from 'vant/lib/locale/lang/en-US';
import zhHK from 'vant/lib/locale/lang/zh-HK';

export const VantPlugin = {
  install(app: App, options: { i18n: { global: unknown } }) {
    const locale = (options.i18n.global as unknown as Composer).locale.value;

    switch (locale) {
      case 'en':
        Locale.use('en-US', enUS);
        break;
      default:
        Locale.use('zh-Hant', zhHK);
        break;
    }

    app.use(Vant);
  },
};

import { ref } from 'vue';
import { nanoid } from 'nanoid';
import URI from 'urijs';

import { NativeEvent } from '@/config/native-event';
import { MessageStatus } from '@/consts/message-status';
import { ChatMode } from '@/consts/chat-mode';
import { AppSource } from '@/consts/app-source';
import { CustomEventName } from '@/consts/custom-event-name';
import { RoleType } from '@/consts/role-type';
import { DeviceType } from '@/consts/device-type';
import { MessageType } from '@/consts/message-type';
import { isIOS } from '@/utils/is';
import { convertPCLocale } from '@/helps/locale';
import { useRealtime } from './realtime';
import { useMessage } from './message';
import { useMessageHistory } from './message-history';
import { useAudioPlayer } from './audio-player';
import { useAudioRecorder } from './audio-recorder';
import { useTypewriter } from './typewriter';
import { usePreCache } from './pre-cache';


// 获取接口需要参数
function getCustomPayloadMessage() {
  return {
    type: 'custom.payload',
    sessionId: nanoid(),
    payload: {
      authorization: native.user.getAccessToken(),
      lang: convertPCLocale(native.i18n.language),
      userId: native.user.getUserId(),
      source: DeviceType.APP,
      appSource: isIOS() ? AppSource.IOS : AppSource.ANDROID,
    },
  };
}

// 获取realtime地址
function getRealtimeEndpoint() {
  const uri = new URI(process.env.AI_MARY_API_BASE_URL);

  uri.segment('realtime');

  return uri.href();
}

// 打印
function log(str: string) {
  console.log(str);
  native.logger.log(str);
}

export function useChat() {
  // 当前对话模式
  const currentMode = ref<ChatMode>(ChatMode.SPEECH);
  // ws连接状态
  const isConnecting = ref(true);
  // ws连接错误
  const isConnectError = ref(false);
  // 是否正在录音
  const isRecording = ref(true);
  // 音量大小
  const volume = ref(0);
  // AI是否正在回复（语音或文本）
  const isAiReplying = ref(false);
  // 文本输入内容
  const inputValue = ref('');
  // 是否静音
  const isMuted = ref(false);
  // 预缓存实例
  const preCache = usePreCache();
  // message实例
  const message = useMessage();
  // 标识不在当前页面的状态
  const isNotCurrentPage = ref(false);

  // 历史消息
  const messageHistory = useMessageHistory({
    insertData: (data) => {
      message.messages.value.unshift(...data);
    },
  });

  // 完成消息
  function completeMessage(messageId: number | string) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    if (currentMode.value === ChatMode.INPUT || isMuted.value || !audioPlayer.getIsPlaying()) {
      isAiReplying.value = false;
    }

    message.clearCurrentAiReplyingMessage();
    message.updateMessageStatus(messageId, MessageStatus.COMPLETE);
  }

  // 打字机实例
  const typewriter = useTypewriter({
    onStop: (messageId) => {
      log('打字机停止');
      completeMessage(messageId);
    },
    onComplete: (messageId) => {
      log('打字机完成');
      completeMessage(messageId);
      inputValue.value = '';
    },
    onContentUpdate: (messageId, content) => {
      // 直接设置消息内容（打字机效果）
      const target = message.messages.value.find((msg) => msg.id === messageId);
      if (target) {
        target.content = content;
      }
    },
  });

  // 音频播放器
  const audioPlayer = useAudioPlayer({
    onCompleted: () => {
      if (!typewriter.getIsTyping()) {
        log('语音播放完成');
        isAiReplying.value = false;
      }
    },
  });

  /**
   * 打断AI回复
   * 停止AI的语音播放和文本输出
   */
  async function interruptAiReply(target?: Chat.Message) {
    log('打断AI回复');

    if (target?.isUtilCall) {
      // 取消AI回复
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      // realtime.conversationItemTruncate(target.id as string);
      // 工具消息删除即可
      message.removeMessage(target.id as string);
    }

    preCache.stopAll();

    // 停止音频播放
    audioPlayer.stop();
    // 停止打字机效果
    typewriter.stop(target?.id);

    // 清空音频缓冲区
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    // realtime.inputAudioBufferClear();
    // 清理当前AI回复的消息
    message.clearCurrentAiReplyingMessage();
    // 清理空消息
    message.clearEmptyMessage();
    isAiReplying.value = false;
  }

  /**
   * 判断是否需要播放音频
   *
   * @param base64Audio base64格式音频数据
   * @param isFinish 是否是最后一段音频
   */
  function playAudioIfNeeded(base64Audio: string, isFinish: boolean = false) {
    if (!isMuted.value
      && isAiReplying.value
      && !isNotCurrentPage.value
      && currentMode.value === ChatMode.SPEECH
    ) {
      if (isFinish) {
        log('语音增量完成');
      }

      audioPlayer.play(base64Audio, isFinish);
    }
  }

  /**
   * Realtime实例
   */
  const realtime = useRealtime({
    wsEndpoint: getRealtimeEndpoint(),

    // 连接事件 - 简化处理，参考demo项目
    onWebSocketOpen: () => {
      log('ws连接成功');
      // 发送后端需要参数
      realtime.sendJsonMessage(getCustomPayloadMessage());
      setTimeout(() => {
        isConnecting.value = false;
        isConnectError.value = false;

        if (currentMode.value === ChatMode.SPEECH) {
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          audioRecorder.start();
        }
      }, 2000);
      audioPlayer.reset();
    },
    onWebSocketClose: () => {
      log('ws连接关闭');
      isConnecting.value = false;
    },
    onWebSocketError: () => {
      log('ws连接错误');
      isConnecting.value = false;
      isConnectError.value = true;

      // 停止当前AI消息状态
      if (message.hasCurrentAiReplyingMessage()) {
        const currentAiReplyingMessage = message.getCurrentAiReplyingMessage();
        message.updateMessageStatus(currentAiReplyingMessage!.id, MessageStatus.COMPLETE);
        message.clearCurrentAiReplyingMessage();
      }
    },

    // realtime事件
    // 对话项创建
    onReceivedConversationItemCreated: (event: Realtime.ConversationItemCreated) => {
      if (event.item.role === RoleType.ASSISTANT) {
        log(`AI消息创建:${event.item.id}`);
        // AI开始回复
        isAiReplying.value = true;
        // 针对工具调用事件，更换后端ID为大模型返回的真正ID，给到后面做增量更新
        const currentAiReplyingMessage = message.getCurrentAiReplyingMessage();
        if (currentAiReplyingMessage?.isUtilCall) {
          log('更换工具调用消息ID');
          currentAiReplyingMessage.id = event.item.id;
          currentAiReplyingMessage.isUtilCall = false;
          currentAiReplyingMessage.type = MessageType.MDX;
          typewriter.start(currentAiReplyingMessage);
        } else {
          log('添加新的AI消息');
          // 添加新的AI消息
          message.setCurrentAiReplyingMessage(message.addAiMessage(event.item.id, event.previous_item_id));
          // 开启打字机效果
          typewriter.start(message.getCurrentAiReplyingMessage()!);
        }
      } else if (event.item.role === RoleType.USER && currentMode.value === ChatMode.SPEECH) {
        log(`用户消息创建:${event.item.id}`);
        message.addUserMessage('', event.item.id, MessageStatus.LOADING);
      }
    },
    // 用户开始说话 - 停止AI播放
    onReceivedInputAudioBufferSpeechStarted: () => {
      log('用户开始说话');
      if (isAiReplying.value) {
        interruptAiReply(message.getCurrentAiReplyingMessage()!);
      }
    },
    // 用户语音转录完成
    onReceivedInputAudioTranscriptionCompleted: (event: Realtime.ResponseInputAudioTranscriptionCompleted) => {
      log(`用户语音转录完成:${event.transcript}`);
      message.updateMessageStatus(event.item_id, MessageStatus.COMPLETE);
      message.appendMessageContent(event.item_id, event.transcript);
    },
    // AI语音增量 - 实时播放
    onReceivedResponseAudioDelta: (event: Realtime.ResponseAudioDelta) => {
      log('AI语音增量');
      const cache = preCache.getCache(event.item_id);
      cache.setExecFull(false);
      cache.addEvent(() => {
        playAudioIfNeeded(event.delta);
      });
    },
    // AI语音转录增量 - 显示AI说话的文本
    onReceivedResponseAudioTranscriptDelta: (event: Realtime.ResponseAudioTranscriptDelta) => {
      log(`AI文本增量-${event.item_id}: ${event.delta}`);

      const target = message.findMessageById(event.item_id);
      if (target && target.status === MessageStatus.COMPLETE) {
        return;
      }

      const cache = preCache.getCache(event.item_id);
      cache.setExecFull(true);
      cache.addEvent(() => {
        message.updateMessageStatus(event.item_id, MessageStatus.INCOMPLETE);
        typewriter.addContext(event.delta, event.item_id);
      });
    },
    // 工具调用开始
    onRunStarted: (event: Realtime.RunStarted) => {
      log('工具调用开始');
      // 如果当前页面有工具调用的loading，则阻断
      const currentAiReplyingMessage = message.getCurrentAiReplyingMessage();
      if (currentAiReplyingMessage?.isUtilCall) {
        return;
      }

      isAiReplying.value = true; // AI开始回复
      message.setCurrentAiReplyingMessage(message.addUtilMessage(event.run_id));
    },
    // 工具调用完成
    onRunFinished: () => {
      log('工具调用完成');
      // removeMessage(message.run_id);
    },

    // 自定义事件
    onCustomEvent: (event: Realtime.CustomEvent) => {
      log(`自定义事件: ${event.name}`);
      switch (event.name) {
        case CustomEventName.OPEN_APP:
          log(`打开应用: ${event.value.serviceCode}`);
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          audioRecorder.stop();
          audioPlayer.stop();
          isNotCurrentPage.value = true;
          native.navigator.v2.launchService({
            serviceCode: event.value.serviceCode,
          });
          break;

        default:
          break;
      }
    },

    // 响应完成
    onReceivedResponseDone: () => {
      // 标识无文本增量 - 完成当前AI回复消息的打字机效果
      const currentAiMessage = message.getCurrentAiReplyingMessage();
      log(`响应完成-${currentAiMessage?.id}`);
      if (currentAiMessage?.id) {
        // 标记预缓存完成
        preCache.getCache(currentAiMessage.id as string).markComplete(
          () => {
            typewriter.completeContext(currentAiMessage.id);
            // 标识无音频增量
            playAudioIfNeeded('', true);
          },
        );
      } else {
        typewriter.completeContext(); // 如果没有当前消息，完成最后一个任务
      }
    },
  });

  /**
   * 录音回调
   * @param base64Audio base64格式音频数据
   * @param volumeValue 音量
   */
  function onAudioRecorded(base64Audio: string, volumeValue: number) {
    if (isRecording.value && currentMode.value === ChatMode.SPEECH) {
      volume.value = volumeValue;
      realtime.addUserAudio(base64Audio);
    } else {
      volume.value = 0;
    }
  }

  /**
   * 录音实例
   */
  const audioRecorder = useAudioRecorder({
    onAudioRecorded,
  });

  /**
   * 切换录音状态
   */
  function onToggleRecording() {
    log(`切换录音状态:${!isRecording.value}`);

    // 由于原生问题（语音播放时关闭录音会导致播放音量变小），暂时不关闭录音
    // if (!isRecording.value) {
    //   startAudioRecording();
    // } else {
    //   // 停止录音
    //   stopAudioRecording();
    // }

    isRecording.value = !isRecording.value;
  }

  /**
   * 切换播放器状态
   */
  async function onToggleMuted() {
    log(`切换播放器状态:${!isMuted.value}`);
    isMuted.value = !isMuted.value;

    if (isMuted.value) {
      audioPlayer.stop();
    } else {
      audioPlayer.reset();
    }

    // updateSessionConfig(!isMuted.value);
  }

  /**
   * 切换对话模式
   */
  function onSwitchMode(mode: ChatMode) {
    log(`切换对话模式:${mode}`);
    realtime.inputAudioBufferClear();
    currentMode.value = mode;
    // updateSessionConfig(mode === ChatMode.SPEECH);
    if (mode === ChatMode.SPEECH) {
      audioRecorder.start();
    } else {
      audioRecorder.stop();
    }
  }

  /**
   * 发送文本消息
   */
  async function onSendMessage() {
    const value = inputValue.value.trim();

    if (!value) {
      return;
    }

    isAiReplying.value = true;

    // 添加用户消息到界面
    message.addUserMessage(value);
    // 发送到服务器
    realtime.sendTextMessage(value);
  }

  /**
   * 停止AI响应
   */
  async function onInterruptAiReply() {
    await interruptAiReply(message.getCurrentAiReplyingMessage()!);
  }

  native.navigator.addEventListener(NativeEvent.WEBVIEW_RESUME, () => {
    isNotCurrentPage.value = false;

    const currentAiReplyingMessage = message.getCurrentAiReplyingMessage();
    if (currentAiReplyingMessage && typewriter.getIsContextComplete(currentAiReplyingMessage.id)) {
      typewriter.stop(currentAiReplyingMessage.id);
    }

    if (isRecording.value && currentMode.value === ChatMode.SPEECH) {
      audioRecorder.start();
      audioPlayer.reset();
    }

    if (isAiReplying.value) {
      isAiReplying.value = false;
    }
  });

  messageHistory.load();

  return {
    // 状态
    currentMode,
    isConnecting,
    isConnectError,
    isRecording,
    isAiReplying,
    messages: message.messages,
    volume,
    inputValue,
    isMuted,
    historyLoading: messageHistory.loading,
    historyInitLoading: messageHistory.initLoading,

    // 操作方法
    onInterruptAiReply,
    onSwitchMode,
    onSendMessage,
    onToggleRecording,
    onToggleMuted,
    loadMessagesHistory: messageHistory.load,
  };
}

/* eslint-disable class-methods-use-this */
// eslint-disable-next-line import/prefer-default-export
export class NativeFile {
  limit: number;

  constructor(options: { limit?: number } = {}) {
    this.limit = options.limit ?? this.defaultLimit();
  }

  defaultLimit() {
    return 20 * 1024 * 1024;
  }

  previewFile(fileUri: any, options = {}) {
    return new Promise<void>((resolve, reject) => {
      native.file.preview(fileUri, options, () => {
        reject();
      });

      resolve();
    });
  }

  chooseFile(options: any) {
    return new Promise((resolve, reject) => {
      native.file.chooseFile(options, (file: any) => {
        const { size } = native.file.getFileInfo(file.path);
        if (this.limit > size) {
          resolve(file);
        } else {
          reject();
        }
      });
    });
  }
}

/* eslint-disable vue/one-component-per-file */
import 'vant/es/toast/style';
import { createApp, h, type ComponentPublicInstance, type App } from 'vue';
import { showToast } from 'vant';
import _ from 'lodash';
import { i18n } from '@/i18n';

import ToastLoading from '@/components/common/toast-loading.vue';
import ToastSuccess from '@/components/common/toast-success.vue';
import ToastError from '@/components/common/toast-error.vue';

interface ToastOptions {
  title?: string;
  message?: string;
  container?: string; // 容器选择器，默认为 'body'
  duration?: number; // 持续时间，默认为 0
}

export const openToast = (message: string, options = {}) => {
  const position = _.get(options, 'position', 'bottom');
  const duration = _.get(options, 'duration', 3000);
  showToast({
    message,
    duration,
    position,
  });
};

export const openToastLoading = (options?: ToastOptions) => {
  const container = _.get(options, 'container', 'body');
  const duration = _.get(options, 'duration', 0);
  let vm: App<Element> | null = createApp(ToastLoading);
  const containerEl = document.querySelector(container);
  const wrapperEl = document.createElement('div');
  containerEl?.append(wrapperEl);
  const appDom = vm.mount(wrapperEl);

  let executed = false;
  let closePromise: Promise<void>;
  const close = () => {
    if (!executed) {
      executed = true;

      closePromise = new Promise<void>((resolve) => {
        setTimeout(() => {
          vm?.unmount();
          vm = null;
          appDom.$el.remove();
          resolve();
        }, duration);
      });
    }

    return closePromise;
  };

  return close;
};

export const openToastSuccess = (options: ToastOptions) => {
  const title = _.get(options, 'title', i18n.global.t('common.hint.operationSucceed'));
  const container = _.get(options, 'container', 'body');
  const duration = _.get(options, 'duration', 3000);
  return new Promise((resolve) => {
    let appDom: ComponentPublicInstance;
    let vm: App<Element> | null = createApp({
      render: () => h(ToastSuccess, {
        duration,
        title,
        on: {
          hide() {
            resolve(0);
            setTimeout(() => {
              vm?.unmount();
              vm = null;
              appDom.$el?.remove();
            }, duration);
          },
        },
      }),
    });

    const containerEl = document.querySelector(container);
    const wrapperEl = document.createElement('div');
    containerEl?.append(wrapperEl);
    appDom = vm.mount(wrapperEl);
  });
};

export const openToastError = (options: ToastOptions) => {
  const title = _.get(options, 'title', i18n.global.t('common.hint.operationFailed'));
  const message = _.get(options, 'message', undefined);
  const container = _.get(options, 'container', 'body');
  const duration = _.get(options, 'duration', 3000);
  return new Promise((resolve) => {
    let appDom: ComponentPublicInstance;
    let vm: App<Element> | null = createApp({
      render: () => h(ToastError, {
        duration,
        title,
        message,
        on: {
          hide() {
            resolve(0);
            setTimeout(() => {
              vm?.unmount();
              vm = null;
              appDom.$el?.remove();
            }, duration);
          },
        },
      }),
    });

    const containerEl = document.querySelector(container);
    const wrapperEl = document.createElement('div');
    containerEl?.append(wrapperEl);
    appDom = vm.mount(wrapperEl);
  });
};

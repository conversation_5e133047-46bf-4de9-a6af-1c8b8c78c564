import { ref } from 'vue';
import { nanoid } from 'nanoid';
import { RoleType } from '@/consts/role-type';
import { MessageStatus } from '@/consts/message-status';
import { MessageType } from '@/consts/message-type';

export function useMessage() {
  const messages = ref<Chat.Message[]>([]);
  const currentAiReplyingMessage = ref<Chat.Message | null>(null);

  function addMessage(message: Chat.Message, previousMessageId?: string): Chat.Message {
    if (previousMessageId) {
      const index = messages.value.findIndex((msg) => msg.id === previousMessageId);
      console.log('上一条消息位置:', index, '--', messages.value[index]?.content);
      native.logger.log(`上一条消息位置:${index}--${messages.value[index]?.content}`);
      if (index !== -1) {
        messages.value.splice(index + 1, 0, message);
        return messages.value[index + 1];
      }
    }

    messages.value.push(message);
    return messages.value[messages.value.length - 1];
  }

  function removeMessage(messageId: string | number) {
    const index = messages.value.findIndex((msg) => msg.id === messageId);
    if (index !== -1) {
      if (currentAiReplyingMessage.value?.id === messageId) {
        currentAiReplyingMessage.value = null;
      }

      messages.value.splice(index, 1);
    }
  }

  // 根据ID查找消息
  function findMessageById(messageId: string | number): Chat.Message | undefined {
    return messages.value.find((msg) => msg.id === messageId);
  }

  // 追加消息内容（用于流式文本）
  function appendMessageContent(messageId: string | number, content: string): void {
    const message = findMessageById(messageId);
    if (message) {
      message.content += content;
    }
  }

  // 更新消息状态
  function updateMessageStatus(messageId: string | number, status: MessageStatus): void {
    const message = findMessageById(messageId);
    if (message) {
      message.status = status;
    }
  }

  // 添加用户消息
  function addUserMessage(content: string, messageId?: string, status: MessageStatus = MessageStatus.COMPLETE):
  Chat.Message {
    return addMessage({
      id: messageId ?? nanoid(),
      type: MessageType.TEXT,
      role: RoleType.USER,
      content,
      status,
      createdAt: Date.now(),
    });
  }

  // 添加AI消息
  function addAiMessage(messageId?: string, previousMessageId?: string): Chat.Message {
    return addMessage({
      id: messageId ?? nanoid(),
      type: MessageType.MDX,
      role: RoleType.ASSISTANT,
      content: '',
      status: MessageStatus.LOADING,
      createdAt: Date.now(),
      previousMessageId,
    }, previousMessageId);
  }

  function addUtilMessage(messageId?: string): Chat.Message {
    return addMessage({
      id: messageId ?? nanoid(),
      type: MessageType.TEXT,
      role: RoleType.ASSISTANT,
      content: '',
      status: MessageStatus.LOADING,
      createdAt: Date.now(),
      isUtilCall: true,
    });
  }

  // 清理当前AI回复的消息
  function clearCurrentAiReplyingMessage() {
    currentAiReplyingMessage.value = null;
  }

  // 更新当前AI回复的消息
  function updateCurrentAiReplyingMessage(message: Partial<Chat.Message>) {
    if (currentAiReplyingMessage.value) {
      Object.assign(currentAiReplyingMessage.value, message);
    }
  }

  // 设置当前AI回复的消息
  function setCurrentAiReplyingMessage(message: Chat.Message) {
    currentAiReplyingMessage.value = message;
  }

  // 是否有当前AI回复的消息
  function hasCurrentAiReplyingMessage(): boolean {
    return !!currentAiReplyingMessage.value;
  }

  // 获取当前AI回复的消息
  function getCurrentAiReplyingMessage(): Chat.Message | null {
    return currentAiReplyingMessage.value;
  }

  // 情况content为空的message
  function clearEmptyMessage() {
    messages.value = messages.value.filter((msg) => msg.content);
  }

  return {
    messages,

    addMessage,
    removeMessage,
    findMessageById,
    appendMessageContent,
    updateMessageStatus,
    addUserMessage,
    addAiMessage,
    addUtilMessage,

    clearCurrentAiReplyingMessage,
    updateCurrentAiReplyingMessage,
    setCurrentAiReplyingMessage,
    hasCurrentAiReplyingMessage,
    getCurrentAiReplyingMessage,
    clearEmptyMessage,
  };
}

import { AxiosError, AxiosResponse } from 'axios';
import { BaseError } from '@/errors/base-error';
import { AccessTokenInvalidError } from '@/errors/access-token-invalid';
import { RefreshTokenInvalidError } from '@/errors/refresh-token-invalid';
import { AccountDisabledError } from '@/errors/account-disabled';
import { namespaceT } from '@/helps/namespace-t';


interface IRefreshTokenError extends AxiosError {
  response: AxiosResponse & {
    data: {
      errorList: {
        INVALID: string;
      };
    };
  };
}

enum ErrorCode {
  ACCESS_CHECK = 'ACCESS_CHECK',
  AUTHORIZATION_NULL_AND_VOID = 'AUTHORIZATION_NULL_AND_VOID',
  INVALID = 'INVALID',
  ACCOUNT_DISABLE = 'ACCOUNT_DISABLE',
  ACCOUNT_FREEZE_OR_DEACTIVATE = 'ACCOUNT_FREEZE_OR_DEACTIVATE',
  ACCOUNT_EXCEPTION = 'ACCOUNT_EXCEPTION',
  ECONNABORTED = 'ECONNABORTED',
  OTHER = 'OTHER',
}

export function errorInterceptor(error: AxiosError) {
  // 超时
  if (error.response?.status === 408
    || error.code === AxiosError.ETIMEDOUT
    || (error.code === ErrorCode.ECONNABORTED && /timeout of \d+ms exceeded$/.test(error.message))) {
    const t = namespaceT('common.error');
    const err = new BaseError(t('timeoutError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // Network Error 由浏览器抛出 （chrome | safari | edge | firefox 测试可用）
  if (error.code === AxiosError.ERR_NETWORK
    || error.message === 'Network Error') {
    const t = namespaceT('common.error');
    throw new BaseError(t('networkError'));
  }

  // 未知错误
  if (error.code === ErrorCode.OTHER) {
    const t = namespaceT('common.error');
    const err = new BaseError(t('unknownError'));
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // 用户AccessToken失效
  if ([
    ErrorCode.ACCESS_CHECK,
    ErrorCode.AUTHORIZATION_NULL_AND_VOID,
  ].includes(error.code as ErrorCode)) {
    const t = namespaceT('common.error');
    throw new AccessTokenInvalidError(t('accountExpiredError'));
  }

  // 用户RefreshToken失效
  if (error.code === ErrorCode.INVALID
    && (error as IRefreshTokenError).response.data?.errorList?.INVALID === 'refreshToken') {
    const t = namespaceT('common.error');
    throw new RefreshTokenInvalidError(t('accountExpiredError'));
  }

  if ([
    ErrorCode.ACCOUNT_DISABLE,
    ErrorCode.ACCOUNT_FREEZE_OR_DEACTIVATE,
    ErrorCode.ACCOUNT_EXCEPTION,
  ].includes(error.code as ErrorCode)) {
    throw new AccountDisabledError();
  }

  throw error;
}

export default {
  action: {
    search: 'Search',
    advancedSearch: 'Advanced Search',
    reset: 'Reset',
    cancel: 'Cancel',
    confirm: 'Confirm',
    ok: 'OK',
    selectFile: 'Select File',
    downloadImportTemplate: 'Download Import Template',
    add: 'Add',
    save: 'Save',
    import: 'Import',
    export: 'Export',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    edit: 'Edit',
    back: 'Back',
  },

  table: {
    serial: 'Serial',
    createdTime: 'Created time',
    operation: 'Operation',
  },

  placeholder: {
    search: 'Search',
    select: 'Please select',
    input: 'Please enter',
    all: 'All',
    startDate: 'Start Date',
    endDate: 'End Date',
    startTime: 'Start Time',
    endTime: 'End Time',
  },

  error: {
    thisFieldIsRequired: 'This field is required',
    thisFieldMustBeSelected: 'This field must be selected',
    formatIsIncorrect: 'Format is incorrect',
    privateEmailFormatIsIncorrect: 'Private Email format is incorrect',
    mobileNumberFormatIsIncorrect: 'Mobile Number format is incorrect',
    excelTemplateError: 'Excel template error',
    dataDoesNotExist: 'Data does not exist',
    dataError: 'Data error. Please select again.',
    timeoutError: 'Your request timed out. Please refresh and try again.',
    unknownError: 'Sorry, the program encountered an unknown error, please refresh and try again.',
    networkError: 'The network request is interrupted. Please try refreshing the webpage.',
    accountExpiredError: 'The Account is expired',
  },

  hint: {
    loading: 'Loading',
    noSearchResult: 'No search results available',
    deleting: 'Deleting...',
    dataSaved: 'Record saved',
    savingFailed: 'Failed to save',
    successfullyDeleted: 'Record deleted',
    deletionFailed: 'Failed to delete',
    downloadSuccessful: 'File downloaded',
    downloadFailed: 'Failed to download',
    importSucceeded: 'List imported',
    importFailed: 'Failed to imported',
    exportSucceeded: 'List exported',
    exportFailed: 'Failed to export',
    uploadCompleted: 'File uploaded',
    failedToUpload: 'Failed to upload',
  },

  modal: {
    reminder: 'Reminder',
    areYouSureToDelete: 'Are you sure to delete this?',
  },

  operationInfo: {
    lastEditBy: 'Last edited by',
    createdBy: 'Creator',
  },
};

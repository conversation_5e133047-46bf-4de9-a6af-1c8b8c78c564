import { throttle } from 'lodash';
import { calculateNodeHeight } from '../utils/calculate-node-height';
import { getSizingData } from '../utils/get-sizing-data';

export interface AutoSizeConfig {
  minRows?: number; // 最小行数
  maxRows?: number; // 最大行数
}

export function useAutoSize(dom: HTMLTextAreaElement) {
  let isComposing = false;
  let compositionText = '';

  function autoResize(options: AutoSizeConfig = {}) {
    const config = {
      minRows: 1,
      maxRows: 3,
      ...options,
    };

    const sizingData = getSizingData(dom);
    // 如果正在输入拼音，合并当前值和composition文本
    const valueToCalculate = isComposing && compositionText
      ? dom.value + compositionText
      : dom.value;

    const newHeight = calculateNodeHeight(
      sizingData,
      valueToCalculate,
      config.minRows,
      config.maxRows,
    );

    Object.assign(dom.style, {
      height: `${newHeight}px`,
    });

    const currentRows = Math.round(newHeight / (sizingData?.sizingStyle.lineHeight
      ? parseFloat(String(sizingData.sizingStyle.lineHeight)) : 24));

    return {
      newHeight,
      currentRows,
    };
  }

  const throttledAutoResize = throttle(autoResize, 10);

  // 设置composition事件监听
  function setupCompositionEvents() {
    const handleCompositionStart = () => {
      isComposing = true;
    };

    const handleCompositionUpdate = (e: CompositionEvent) => {
      isComposing = true;
      compositionText = e.data;
      // 触发自动调整大小
      throttledAutoResize();
    };

    const handleCompositionEnd = () => {
      isComposing = false;
      compositionText = '';
      // 触发自动调整大小
      throttledAutoResize();
    };

    dom.addEventListener('compositionstart', handleCompositionStart);
    dom.addEventListener('compositionupdate', handleCompositionUpdate);
    dom.addEventListener('compositionend', handleCompositionEnd);

    // 返回清理函数
    return () => {
      dom.removeEventListener('compositionstart', handleCompositionStart);
      dom.removeEventListener('compositionupdate', handleCompositionUpdate);
      dom.removeEventListener('compositionend', handleCompositionEnd);
    };
  }

  const cleanupComposition = setupCompositionEvents();

  return {
    autoResize: throttledAutoResize,
    cleanup: cleanupComposition,
  };
}

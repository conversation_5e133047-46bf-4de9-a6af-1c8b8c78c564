import { getSizingData } from './get-sizing-data';

const HIDDEN_TEXTAREA_STYLE = {
  'min-height': '0',
  'max-height': 'none',
  height: '0',
  visibility: 'hidden',
  overflow: 'hidden',
  position: 'absolute',
  'z-index': '-1000',
  top: '0',
  right: '0',
};

const forceHiddenStyles = (node: HTMLTextAreaElement) => {
  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach((key) => {
    node.style.setProperty(
      key,
      HIDDEN_TEXTAREA_STYLE[key as keyof typeof HIDDEN_TEXTAREA_STYLE],
      'important',
    );
  });
};

let hiddenTextarea: HTMLTextAreaElement | null = null;

const getContentHeight = (
  node: HTMLTextAreaElement,
  sizingData: ReturnType<typeof getSizingData>,
) => {
  if (!sizingData) {
    return node.scrollHeight;
  }

  const height = node.scrollHeight;

  if (sizingData.sizingStyle.boxSizing === 'border-box') {
    // border-box: add border, since height = content + padding + border
    return height + sizingData.borderSize;
  }

  // remove padding, since height = content
  return height - sizingData.paddingSize;
};

export function calculateNodeHeight(
  sizingData: ReturnType<typeof getSizingData>,
  value: string,
  minRows = 1,
  maxRows = Infinity,
) {
  if (!hiddenTextarea) {
    hiddenTextarea = document.createElement('textarea');
    hiddenTextarea.setAttribute('tab-index', '-1');
    hiddenTextarea.setAttribute('aria-hidden', 'true');
    forceHiddenStyles(hiddenTextarea);
  }

  if (hiddenTextarea.parentNode === null) {
    document.body.appendChild(hiddenTextarea);
  }

  if (!sizingData) {
    return hiddenTextarea.scrollHeight;
  }

  const { paddingSize, borderSize, sizingStyle } = sizingData;
  const { boxSizing } = sizingStyle;

  Object.keys(sizingStyle).forEach((key) => {
    // @ts-expect-error key is CSSStyleDeclaration
    hiddenTextarea!.style[key] = sizingStyle[key];
  });

  forceHiddenStyles(hiddenTextarea);

  hiddenTextarea.value = value;
  let height = getContentHeight(hiddenTextarea, sizingData);

  // measure height of a textarea with a single row
  hiddenTextarea.value = 'x';
  // calc single row need to remove padding and border to avoid duplicated calc
  const singleRowContentHeight = getContentHeight(hiddenTextarea, sizingData) - paddingSize - borderSize;

  // 使用 CSS line-height 作为基准，如果可用的话
  const cssLineHeight = parseFloat(String(sizingStyle.lineHeight));
  // eslint-disable-next-line no-restricted-globals
  const rowHeight = !isNaN(cssLineHeight) && cssLineHeight > 0
    ? cssLineHeight
    : singleRowContentHeight;

  let minHeight = rowHeight * minRows;
  if (boxSizing === 'border-box') {
    minHeight = minHeight + paddingSize + borderSize;
  }
  height = Math.max(minHeight, height);

  let maxHeight = rowHeight * maxRows;
  if (boxSizing === 'border-box') {
    maxHeight = maxHeight + paddingSize + borderSize;
  }
  height = Math.min(maxHeight, height);

  return height;
}

/**
 * 对话流程控制器
 * 负责管理对话的整体流程，包括状态转换、事件协调等
 */

import { ref, type Ref } from 'vue';
import { ChatMode } from '@/consts/chat-mode';
import { MessageStatus } from '@/consts/message-status';
import { RoleType } from '@/consts/role-type';
import type { MessageManager } from './message-manager';
import type { BaseConnection } from '../connection/base-connection';
import type { InteractionManager } from '../interaction/base-interaction';

/**
 * 对话流程状态枚举
 */
export enum ConversationFlowState {
  IDLE = 'idle',
  USER_INPUT = 'user_input',
  AI_PROCESSING = 'ai_processing',
  AI_RESPONDING = 'ai_responding',
  INTERRUPTED = 'interrupted',
  ERROR = 'error'
}

/**
 * 对话流程控制器
 */
export class ConversationFlow {
  /** 当前流程状态 */
  public readonly state: Ref<ConversationFlowState> = ref(ConversationFlowState.IDLE);
  
  /** 对话状态 */
  public readonly conversationState: Ref<Conversation.ConversationState> = ref({
    mode: ChatMode.SPEECH,
    isConnecting: false,
    hasConnectionError: false,
    isAiReplying: false,
    isRecording: false,
    isMuted: false,
    volume: 0,
    inputText: '',
  });

  /** 消息管理器 */
  private messageManager: MessageManager;
  
  /** 连接实例 */
  private connection: BaseConnection;
  
  /** 交互管理器 */
  private interactionManager: InteractionManager;
  
  /** 事件监听器映射 */
  private eventListeners: Map<string, Set<(...args: unknown[]) => void>> = new Map();

  constructor(
    messageManager: MessageManager,
    connection: BaseConnection,
    interactionManager: InteractionManager
  ) {
    this.messageManager = messageManager;
    this.connection = connection;
    this.interactionManager = interactionManager;
    
    this.setupEventListeners();
  }

  /**
   * 初始化对话流程
   */
  async initialize(): Promise<void> {
    try {
      this.setState(ConversationFlowState.IDLE);
      this.updateConversationState({ isConnecting: false, hasConnectionError: false });
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 开始对话
   */
  async startConversation(mode: ChatMode = ChatMode.SPEECH): Promise<void> {
    try {
      // 更新对话模式
      this.updateConversationState({ mode });
      
      // 启动相应的交互模式
      if (mode === ChatMode.SPEECH) {
        await this.interactionManager.startInteraction(Conversation.InteractionType.VOICE);
      } else {
        await this.interactionManager.startInteraction(Conversation.InteractionType.TEXT);
      }
      
      this.setState(ConversationFlowState.IDLE);
      this.emit('conversationStarted', mode);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 停止对话
   */
  async stopConversation(): Promise<void> {
    try {
      // 停止所有交互
      await this.interactionManager.stopAllInteractions();
      
      // 清理当前AI回复
      if (this.messageManager.hasCurrentAiMessage()) {
        const currentMessage = this.messageManager.getCurrentAiMessage();
        if (currentMessage) {
          this.messageManager.updateMessageStatus(currentMessage.id as string, MessageStatus.COMPLETE);
        }
        this.messageManager.clearCurrentAiMessage();
      }
      
      this.setState(ConversationFlowState.IDLE);
      this.updateConversationState({ isAiReplying: false });
      this.emit('conversationStopped');
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 切换对话模式
   */
  async switchMode(mode: ChatMode): Promise<void> {
    if (this.conversationState.value.mode === mode) {
      return;
    }

    try {
      // 停止当前交互
      await this.interactionManager.stopAllInteractions();
      
      // 清空音频缓冲区（如果支持）
      if ('clearAudioBuffer' in this.connection) {
        await (this.connection as any).clearAudioBuffer();
      }
      
      // 更新模式
      this.updateConversationState({ mode });
      
      // 启动新的交互模式
      if (mode === ChatMode.SPEECH) {
        await this.interactionManager.startInteraction(Conversation.InteractionType.VOICE);
      } else {
        await this.interactionManager.startInteraction(Conversation.InteractionType.TEXT);
      }
      
      this.emit('modeChanged', mode);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 处理用户开始输入
   */
  handleUserInputStart(inputType: Conversation.InteractionType): void {
    this.setState(ConversationFlowState.USER_INPUT);
    
    // 如果AI正在回复，打断AI
    if (this.conversationState.value.isAiReplying) {
      this.interruptAiReply();
    }
    
    this.emit('userInputStarted', inputType);
  }

  /**
   * 处理用户输入完成
   */
  handleUserInputComplete(content: string, messageId: string): void {
    // 更新用户消息
    this.messageManager.updateMessageContent(messageId, content);
    this.messageManager.updateMessageStatus(messageId, MessageStatus.COMPLETE);
    
    this.setState(ConversationFlowState.AI_PROCESSING);
    this.emit('userInputCompleted', content, messageId);
  }

  /**
   * 处理AI开始回复
   */
  handleAiReplyStart(messageId: string): void {
    this.setState(ConversationFlowState.AI_RESPONDING);
    this.updateConversationState({ isAiReplying: true });
    
    this.emit('aiReplyStarted', messageId);
  }

  /**
   * 处理AI回复完成
   */
  handleAiReplyComplete(messageId: string): void {
    // 更新消息状态
    this.messageManager.updateMessageStatus(messageId, MessageStatus.COMPLETE);
    this.messageManager.clearCurrentAiMessage();
    
    this.setState(ConversationFlowState.IDLE);
    this.updateConversationState({ isAiReplying: false });
    
    this.emit('aiReplyCompleted', messageId);
  }

  /**
   * 打断AI回复
   */
  async interruptAiReply(): Promise<void> {
    if (!this.conversationState.value.isAiReplying) {
      return;
    }

    try {
      this.setState(ConversationFlowState.INTERRUPTED);
      
      const currentMessage = this.messageManager.getCurrentAiMessage();
      
      // 停止音频播放（如果是语音模式）
      const voiceInteraction = this.interactionManager.getInteraction(Conversation.InteractionType.VOICE);
      if (voiceInteraction && 'stopAudioPlayback' in voiceInteraction) {
        await (voiceInteraction as any).stopAudioPlayback();
      }
      
      // 处理工具调用消息
      if (currentMessage?.isUtilCall) {
        this.messageManager.removeMessage(currentMessage.id as string);
      }
      
      // 清理当前AI消息
      this.messageManager.clearCurrentAiMessage();
      this.messageManager.clearEmptyMessages();
      
      this.setState(ConversationFlowState.IDLE);
      this.updateConversationState({ isAiReplying: false });
      
      this.emit('aiReplyInterrupted', currentMessage?.id);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 处理工具调用
   */
  handleToolCall(toolCallId: string): void {
    // 添加工具调用消息
    const message = this.messageManager.addUtilMessage(toolCallId);
    this.updateConversationState({ isAiReplying: true });
    
    this.emit('toolCallStarted', toolCallId, message);
  }

  /**
   * 处理工具调用完成
   */
  handleToolCallComplete(toolCallId: string): void {
    this.emit('toolCallCompleted', toolCallId);
  }

  /**
   * 获取当前对话状态
   */
  getConversationState(): Conversation.ConversationState {
    return { ...this.conversationState.value };
  }

  /**
   * 更新对话状态
   */
  updateConversationState(updates: Partial<Conversation.ConversationState>): void {
    Object.assign(this.conversationState.value, updates);
    this.emit('conversationStateChanged', this.conversationState.value);
  }

  /**
   * 注册事件监听器
   */
  on(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: (...args: unknown[]) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: unknown[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`[ConversationFlow] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 设置流程状态
   */
  private setState(newState: ConversationFlowState): void {
    const oldState = this.state.value;
    this.state.value = newState;
    this.emit('flowStateChanged', newState, oldState);
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    console.error('[ConversationFlow] Error:', error);
    this.setState(ConversationFlowState.ERROR);
    this.updateConversationState({ hasConnectionError: true });
    this.emit('error', error);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 连接状态变化
    this.connection.on('stateChange', (state: Conversation.ConnectionState) => {
      this.updateConversationState({
        isConnecting: state === Conversation.ConnectionState.CONNECTING,
        hasConnectionError: state === Conversation.ConnectionState.ERROR,
      });
    });

    // 用户语音开始
    this.connection.on('userSpeechStart', () => {
      this.handleUserInputStart(Conversation.InteractionType.VOICE);
    });

    // 用户语音转录完成
    this.connection.on('userTranscriptionComplete', (transcript: string, messageId: string) => {
      this.handleUserInputComplete(transcript, messageId);
    });

    // AI回复相关事件
    this.connection.on('conversationItemCreated', (message: any) => {
      if (message.item?.role === RoleType.ASSISTANT) {
        this.handleAiReplyStart(message.item.id);
      } else if (message.item?.role === RoleType.USER && this.conversationState.value.mode === ChatMode.SPEECH) {
        // 添加用户语音消息
        this.messageManager.addUserMessage('', message.item.id, MessageStatus.LOADING);
      }
    });

    // 响应完成
    this.connection.on('responseDone', () => {
      const currentMessage = this.messageManager.getCurrentAiMessage();
      if (currentMessage) {
        this.handleAiReplyComplete(currentMessage.id as string);
      }
    });

    // 工具调用事件
    this.connection.on('toolCallStart', (toolCallId: string) => {
      this.handleToolCall(toolCallId);
    });

    this.connection.on('toolCallComplete', (toolCallId: string) => {
      this.handleToolCallComplete(toolCallId);
    });
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopConversation();
    this.eventListeners.clear();
  }
}

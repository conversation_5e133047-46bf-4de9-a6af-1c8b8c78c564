import {
  type RequestConfig,
  type RequestMethod,
  type <PERSON>questParams,
  type RequestData,
  BaseRequestApi,
} from './base-request-api';

export { type RequestConfig, type RequestMethod, type RequestParams, type RequestData };


export class BaseUploadRequest<PERSON><PERSON><T> extends BaseRequestApi<T> {
  constructor(args?: RequestConfig) {
    super({
      ...args,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  method(): RequestMethod {
    return 'POST';
  }
}

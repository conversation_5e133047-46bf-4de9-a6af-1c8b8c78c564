
/**
 * WebSocket 配置接口
 */
export interface WebSocketConfig {
  /** WebSocket 连接端点 URL */
  wsEndpoint: string;
  /** WebSocket 连接成功时的回调函数 */
  onOpen?: () => void;
  /** WebSocket 连接关闭时的回调函数 */
  onClose?: () => void;
  /** WebSocket 连接错误时的回调函数 */
  onError?: (event: Event) => void;
  /** 接收到 WebSocket 消息时的回调函数 */
  onMessage?: (event: MessageEvent) => void;
  /** 判断是否应该重连的函数，返回 true 表示应该重连 */
  shouldReconnect?: () => boolean;
  /** 是否自动连接，默认为 true */
  autoConnect?: boolean;
  /** 最大重试次数，默认为 5 */
  maxRetries?: number;
  /** 重连延迟时间(毫秒)，默认为 3000 */
  reconnectDelay?: number;
  /** 是否启用心跳机制，默认为 true */
  enableHeartbeat?: boolean;
  /** 心跳间隔时间(毫秒)，默认为 30000 (30秒) */
  heartbeatInterval?: number;
  /** 心跳超时时间(毫秒)，默认为 5000 (5秒) */
  heartbeatTimeout?: number;
  /** 心跳消息内容，默认为 "ping" */
  heartbeatMessage?: string;
  /** 心跳超时时的回调函数 */
  onHeartbeatTimeout?: () => void;
}

/**
 * WebSocket 连接状态枚举
 */
export enum WebSocketReadyState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

/**
 * WebSocket Hook
 * 提供 WebSocket 连接管理和消息通信功能
 *
 * @param config - WebSocket 配置参数
 * @returns 返回连接控制方法和状态查询函数
 */
export function useWebSocket(config: WebSocketConfig) {
  const {
    wsEndpoint,
    onOpen,
    onClose,
    onError,
    onMessage,
    shouldReconnect = () => true,
    autoConnect = true,
    maxRetries = 5,
    reconnectDelay = 3000,
    enableHeartbeat = false,
    heartbeatInterval = 30000,
    heartbeatTimeout = 5000,
    heartbeatMessage = 'ping',
    onHeartbeatTimeout,
  } = config;

  // 内部状态管理
  let websocket: WebSocket | null = null;
  let reconnectTimer: number | null = null;
  let retryCount = 0;
  let isManualClose = false;
  let messageQueue: string[] = [];

  // 心跳机制相关状态
  let heartbeatTimer: number | null = null;
  let heartbeatTimeoutTimer: number | null = null;
  let isWaitingHeartbeatResponse = false;

  /**
   * 构建 WebSocket URL
   * 支持相对路径、完整 URL 或 http/https 自动转换
   */
  function buildWebSocketUrl(endpoint: string): string {
    let url = endpoint;

    // 如果是相对路径，构建完整URL
    if (endpoint.startsWith('/')) {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const { host } = window.location;
      url = `${protocol}//${host}${endpoint}`;
    } else if (endpoint.startsWith('ws://') || endpoint.startsWith('wss://')) {
      // 如果是完整URL，直接使用
      url = endpoint;
    } else if (endpoint.startsWith('http://')) {
      // 如果是http URL，转换为ws
      url = endpoint.replace('http://', 'ws://');
    } else if (endpoint.startsWith('https://')) {
      // 如果是https URL，转换为wss
      url = endpoint.replace('https://', 'wss://');
    }

    return url;
  }

  /**
   * 启动心跳机制
   */
  function startHeartbeat(): void {
    if (!enableHeartbeat || heartbeatTimer) {
      return;
    }

    heartbeatTimer = window.setInterval(() => {
      if (websocket?.readyState === WebSocket.OPEN && !isWaitingHeartbeatResponse) {
        isWaitingHeartbeatResponse = true;

        // 发送心跳消息
        websocket.send(heartbeatMessage);

        // 设置心跳超时定时器
        heartbeatTimeoutTimer = window.setTimeout(() => {
          console.warn('[WebSocket] 心跳超时，准备重连');
          onHeartbeatTimeout?.();

          // 心跳超时，强制重连
          if (websocket) {
            websocket.close();
          }
        }, heartbeatTimeout);
      }
    }, heartbeatInterval);
  }

  /**
   * 停止心跳机制
   */
  function stopHeartbeat(): void {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }

    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer);
      heartbeatTimeoutTimer = null;
    }

    isWaitingHeartbeatResponse = false;
  }

  /**
   * 处理心跳响应
   */
  function handleHeartbeatResponse(): void {
    if (isWaitingHeartbeatResponse) {
      isWaitingHeartbeatResponse = false;

      if (heartbeatTimeoutTimer) {
        clearTimeout(heartbeatTimeoutTimer);
        heartbeatTimeoutTimer = null;
      }
    }
  }

  /**
   * 连接到 WebSocket 服务器
   *
   * @returns Promise<void> - 连接成功时 resolve，失败时 reject
   */
  function connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (websocket?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      try {
        const url = buildWebSocketUrl(wsEndpoint);
        websocket = new WebSocket(url);

        websocket.onopen = () => {
          console.log('[WebSocket] 连接成功');
          retryCount = 0;
          isManualClose = false;

          // 发送队列中的消息
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          flushMessageQueue();

          // 启动心跳机制
          startHeartbeat();

          onOpen?.();
          resolve();
        };

        websocket.onclose = (event) => {
          console.log('[WebSocket] 连接关闭', event.code, event.reason);

          // 停止心跳机制
          stopHeartbeat();

          onClose?.();

          // 如果不是手动关闭且应该重连，则尝试重连
          if (!isManualClose && shouldReconnect() && retryCount < maxRetries) {
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            scheduleReconnect();
          }
        };

        websocket.onerror = (event) => {
          console.error('[WebSocket] 连接错误', event);
          onError?.(event);
          reject(new Error('WebSocket connection failed'));
        };

        websocket.onmessage = (event) => {
          // 检查是否是心跳响应消息
          if (enableHeartbeat && event.data === 'pong') {
            handleHeartbeatResponse();
            return;
          }

          onMessage?.(event);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 安排重连
   */
  function scheduleReconnect(): void {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
    }

    retryCount += 1;
    console.log(`[WebSocket] 准备重连，第 ${retryCount}/${maxRetries} 次尝试`);

    reconnectTimer = window.setTimeout(() => {
      connect().catch((error) => {
        console.error('[WebSocket] 重连失败:', error);
      });
    }, reconnectDelay);
  }

  /**
   * 发送队列中的消息
   */
  function flushMessageQueue(): void {
    if (messageQueue.length > 0 && websocket?.readyState === WebSocket.OPEN) {
      console.log(`[WebSocket] 发送队列中的 ${messageQueue.length} 条消息`);

      while (messageQueue.length > 0) {
        const message = messageQueue.shift();
        if (message) {
          websocket.send(message);
        }
      }
    }
  }

  /**
   * 断开 WebSocket 连接
   */
  function disconnect(): void {
    isManualClose = true;

    // 停止心跳机制
    stopHeartbeat();

    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }

    if (websocket) {
      websocket.close();
      websocket = null;
    }

    // 清空消息队列
    messageQueue = [];
    retryCount = 0;
  }

  /**
   * 发送字符串消息
   *
   * @param message - 要发送的字符串消息
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function sendMessage(message: string): boolean {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      console.warn('[WebSocket] 连接未建立，消息已加入队列');
      messageQueue.push(message);
      return false;
    }

    try {
      websocket.send(message);
      return true;
    } catch (error) {
      console.error('[WebSocket] 发送消息失败:', error);
      return false;
    }
  }

  /**
   * 发送 JSON 消息
   *
   * @param data - 要发送的数据对象，会被序列化为 JSON
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function sendJsonMessage(data: unknown): boolean {
    try {
      const message = JSON.stringify(data);
      return sendMessage(message);
    } catch (error) {
      console.error('[WebSocket] JSON 序列化失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已连接
   *
   * @returns boolean - 已连接返回 true，否则返回 false
   */
  function isConnected(): boolean {
    return websocket?.readyState === WebSocket.OPEN;
  }

  /**
   * 获取当前连接状态
   *
   * @returns WebSocketReadyState - 当前连接状态
   */
  function getReadyState(): WebSocketReadyState {
    return websocket?.readyState ?? WebSocketReadyState.CLOSED;
  }

  /**
   * 获取重试次数
   *
   * @returns number - 当前重试次数
   */
  function getRetryCount(): number {
    return retryCount;
  }

  /**
   * 获取队列中的消息数量
   *
   * @returns number - 队列中的消息数量
   */
  function getQueueSize(): number {
    return messageQueue.length;
  }

  /**
   * 获取心跳状态
   *
   * @returns boolean - 心跳是否正在运行
   */
  function isHeartbeatRunning(): boolean {
    return heartbeatTimer !== null;
  }

  /**
   * 获取是否正在等待心跳响应
   *
   * @returns boolean - 是否正在等待心跳响应
   */
  function isWaitingHeartbeat(): boolean {
    return isWaitingHeartbeatResponse;
  }

  // 自动连接
  if (autoConnect && wsEndpoint) {
    connect().catch((error) => {
      console.error('[WebSocket] 自动连接失败:', error);
    });
  }

  return {
    // 连接控制方法
    /** 连接到 WebSocket 服务器 */
    connect,
    /** 断开 WebSocket 连接 */
    disconnect,

    // 消息发送方法
    /** 发送字符串消息 */
    sendMessage,
    /** 发送 JSON 消息 */
    sendJsonMessage,

    // 状态查询方法
    /** 检查是否已连接 */
    isConnected,
    /** 获取当前连接状态 */
    getReadyState,
    /** 获取重试次数 */
    getRetryCount,
    /** 获取队列中的消息数量 */
    getQueueSize,

    // 心跳相关方法
    /** 获取心跳状态 */
    isHeartbeatRunning,
    /** 获取是否正在等待心跳响应 */
    isWaitingHeartbeat,
  };
}

<script setup lang="ts">
import { computed, ref, onMounted, watch, onBeforeUnmount } from 'vue';

interface Props {
  /** 音量大小 (0-100) */
  volume: number;
  /** 波形颜色 */
  color?: string;
  /** 波形条数量 */
  barCount?: number;
  /** 波形条宽度 */
  width?: number;
  /** 提示文字 */
  tip?: string;
  /** 是否激活动画 */
  animated?: boolean;
  /** 动画帧率控制 (ms) */
  frameInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  volume: 0,
  color: '#4a4a4d',
  barCount: 5,
  width: 8,
  tip: '',
  animated: true,
  frameInterval: 150, // 降低帧率，提高性能
});

// 计算每个波形条的基础高度
const baseHeights = computed(() => {
  const heights: number[] = [];
  const baseHeight = props.width;

  // 根据音量计算高度范围
  const volumeRatio = Math.max(0, Math.min(100, props.volume)) / 100;
  const heightRange = baseHeight * volumeRatio * 4;

  // 为每个条创建不同的高度变化，形成波形效果
  for (let i = 0; i < props.barCount; i += 1) {
    // 中间的条最高，两边递减
    const centerIndex = Math.floor(props.barCount / 2);
    const distanceFromCenter = Math.abs(i - centerIndex);
    const maxDistance = centerIndex || 1; // 防止除以0
    const positionMultiplier = 1 - (distanceFromCenter / maxDistance) * 0.7;

    const height = baseHeight + heightRange * positionMultiplier;
    heights.push(Math.round(height));
  }

  return heights;
});

// 动态高度变化
const animatedHeights = ref<number[]>([]);
const animationActive = ref(false);

// 预计算波形变化模式，减少运行时计算
const getWaveIntensities = computed(() => {
  const intensities: number[] = [];
  const centerIndex = Math.floor(props.barCount / 2);

  for (let i = 0; i < props.barCount; i += 1) {
    const distanceFromCenter = Math.abs(i - centerIndex);
    const maxDistance = centerIndex || 1;
    const waveIntensity = 1 - (distanceFromCenter / maxDistance) * 0.5;
    intensities.push(waveIntensity);
  }

  return intensities;
});

// 更新动画高度
const updateAnimatedHeights = () => {
  if (!props.animated || props.volume <= 5) {
    animatedHeights.value = [...baseHeights.value];
    return;
  }

  const waveIntensities = getWaveIntensities.value;
  const isHighVolume = props.volume > 20;

  animatedHeights.value = baseHeights.value.map((height, index) => {
    // 使用预计算的波形强度
    const waveIntensity = waveIntensities[index];

    // 随机波动范围 - 根据音量调整
    const randomFactor = isHighVolume
      ? 0.85 + Math.random() * 0.3 * waveIntensity
      : 0.95 + Math.random() * 0.1;

    return Math.round(height * randomFactor);
  });
};

let animationTimer: number | null = null;
const startAnimation = () => {
  if (!props.animated || animationActive.value) return;

  animationActive.value = true;
  updateAnimatedHeights(); // 立即更新一次

  // 使用setInterval控制帧率
  animationTimer = window.setInterval(() => {
    updateAnimatedHeights();
  }, props.frameInterval);
};

// 停止动画
const stopAnimation = () => {
  if (animationTimer) {
    clearInterval(animationTimer);
    animationTimer = null;
  }
  animationActive.value = false;
  animatedHeights.value = [...baseHeights.value];
};

// 监听音量变化
watch(() => props.volume, (newVolume) => {
  if (newVolume > 5 && props.animated) {
    startAnimation();
  } else if (newVolume <= 5 && animationActive.value) {
    stopAnimation();
  }
}, { immediate: true });

// 监听基础高度变化，确保在音量变化时更新静态波形
watch(baseHeights, (newHeights) => {
  if (!animationActive.value) {
    animatedHeights.value = [...newHeights];
  }
});

// 组件挂载和卸载时处理动画
onMounted(() => {
  updateAnimatedHeights();
  if (props.volume > 5 && props.animated) {
    startAnimation();
  }
});

onBeforeUnmount(() => {
  stopAnimation();
});

// 计算样式变量
const width = computed(() => `${props.width}px`);
const radius = computed(() => `${Math.round(props.width / 2)}px`);
</script>

<template>
  <div class="voice-waveform">
    <div class="voice-waveform-inner">
      <div
        v-for="(height, index) in animatedHeights"
        :key="index"
        class="voice-waveform-bar"
        :style="{
          height: `${height}px`
        }"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.voice-waveform {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;

  .voice-waveform-inner {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;

    .voice-waveform-bar {
      width: v-bind(width);
      background-color: v-bind(color);
      border-radius: v-bind(radius);
      transform-origin: center;
      transition: height 0.15s ease-out;
    }
  }
}
</style>

/**
 * 交互层基础抽象类
 * 定义通用的交互处理接口，支持语音、文本等不同交互方式
 */

import { ref, type Ref } from 'vue';
import type { BaseConnection } from '../connection/base-connection';

/**
 * 交互状态枚举
 */
export enum InteractionState {
  IDLE = 'idle',
  ACTIVE = 'active',
  PROCESSING = 'processing',
  ERROR = 'error'
}

/**
 * 基础交互抽象类
 */
export abstract class BaseInteraction {
  /** 交互状态 */
  public readonly state: Ref<InteractionState> = ref(InteractionState.IDLE);
  
  /** 连接实例 */
  protected connection: BaseConnection;
  
  /** 交互类型 */
  public readonly type: Conversation.InteractionType;
  
  /** 事件监听器映射 */
  protected eventListeners: Map<string, Set<(...args: unknown[]) => void>> = new Map();

  constructor(connection: BaseConnection, type: Conversation.InteractionType) {
    this.connection = connection;
    this.type = type;
    this.setupConnectionListeners();
  }

  /**
   * 初始化交互
   */
  abstract initialize(): Promise<void>;

  /**
   * 启动交互
   */
  abstract start(): Promise<void>;

  /**
   * 停止交互
   */
  abstract stop(): Promise<void>;

  /**
   * 重置交互状态
   */
  abstract reset(): void;

  /**
   * 获取交互状态
   */
  getState(): InteractionState {
    return this.state.value;
  }

  /**
   * 是否处于活跃状态
   */
  isActive(): boolean {
    return this.state.value === InteractionState.ACTIVE;
  }

  /**
   * 注册事件监听器
   */
  on(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: (...args: unknown[]) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * 触发事件
   */
  protected emit(event: string, ...args: unknown[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`[${this.type}Interaction] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 更新交互状态
   */
  protected setState(newState: InteractionState): void {
    const oldState = this.state.value;
    this.state.value = newState;
    this.emit('stateChange', newState, oldState);
  }

  /**
   * 处理错误
   */
  protected handleError(error: Error): void {
    console.error(`[${this.type}Interaction] Error:`, error);
    this.setState(InteractionState.ERROR);
    this.emit('error', error);
  }

  /**
   * 设置连接事件监听器
   */
  protected setupConnectionListeners(): void {
    this.connection.on('stateChange', (state: Conversation.ConnectionState) => {
      this.handleConnectionStateChange(state);
    });

    this.connection.on('error', (error: Error) => {
      this.handleConnectionError(error);
    });
  }

  /**
   * 处理连接状态变化
   */
  protected handleConnectionStateChange(state: Conversation.ConnectionState): void {
    switch (state) {
      case Conversation.ConnectionState.CONNECTED:
        this.onConnectionEstablished();
        break;
      case Conversation.ConnectionState.DISCONNECTED:
      case Conversation.ConnectionState.ERROR:
        this.onConnectionLost();
        break;
      default:
        break;
    }
  }

  /**
   * 连接建立时的处理
   */
  protected onConnectionEstablished(): void {
    // 子类可以重写此方法
  }

  /**
   * 连接丢失时的处理
   */
  protected onConnectionLost(): void {
    if (this.isActive()) {
      this.stop();
    }
  }

  /**
   * 处理连接错误
   */
  protected handleConnectionError(error: Error): void {
    this.handleError(new Error(`Connection error: ${error.message}`));
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    this.eventListeners.clear();
    this.setState(InteractionState.IDLE);
  }

  /**
   * 销毁交互实例
   */
  async destroy(): Promise<void> {
    await this.stop();
    this.cleanup();
  }
}

/**
 * 交互管理器
 * 负责管理多种交互类型的实例
 */
export class InteractionManager {
  private interactions: Map<Conversation.InteractionType, BaseInteraction> = new Map();
  private activeInteraction: BaseInteraction | null = null;

  /**
   * 注册交互实例
   */
  registerInteraction(interaction: BaseInteraction): void {
    this.interactions.set(interaction.type, interaction);
    
    // 监听交互状态变化
    interaction.on('stateChange', (newState: InteractionState) => {
      this.handleInteractionStateChange(interaction, newState);
    });
  }

  /**
   * 获取交互实例
   */
  getInteraction(type: Conversation.InteractionType): BaseInteraction | undefined {
    return this.interactions.get(type);
  }

  /**
   * 启动指定类型的交互
   */
  async startInteraction(type: Conversation.InteractionType): Promise<void> {
    const interaction = this.interactions.get(type);
    if (!interaction) {
      throw new Error(`Interaction type ${type} not registered`);
    }

    // 如果有其他活跃的交互，先停止
    if (this.activeInteraction && this.activeInteraction !== interaction) {
      await this.activeInteraction.stop();
    }

    await interaction.start();
    this.activeInteraction = interaction;
  }

  /**
   * 停止指定类型的交互
   */
  async stopInteraction(type: Conversation.InteractionType): Promise<void> {
    const interaction = this.interactions.get(type);
    if (interaction) {
      await interaction.stop();
      if (this.activeInteraction === interaction) {
        this.activeInteraction = null;
      }
    }
  }

  /**
   * 停止所有交互
   */
  async stopAllInteractions(): Promise<void> {
    const promises = Array.from(this.interactions.values()).map(interaction => interaction.stop());
    await Promise.all(promises);
    this.activeInteraction = null;
  }

  /**
   * 获取当前活跃的交互
   */
  getActiveInteraction(): BaseInteraction | null {
    return this.activeInteraction;
  }

  /**
   * 处理交互状态变化
   */
  private handleInteractionStateChange(interaction: BaseInteraction, newState: InteractionState): void {
    if (newState === InteractionState.ACTIVE) {
      this.activeInteraction = interaction;
    } else if (newState === InteractionState.IDLE && this.activeInteraction === interaction) {
      this.activeInteraction = null;
    }
  }

  /**
   * 清理所有交互
   */
  async cleanup(): Promise<void> {
    const promises = Array.from(this.interactions.values()).map(interaction => interaction.destroy());
    await Promise.all(promises);
    this.interactions.clear();
    this.activeInteraction = null;
  }
}

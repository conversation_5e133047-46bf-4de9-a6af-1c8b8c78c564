import { RoleType } from '@/consts/role-type';
import { useWebSocket } from './websocket';


/**
 * Realtime API Hook
 * 提供 WebSocket 连接管理和实时音频/文本通信功能
 *
 * @param config - Realtime 配置参数，包含 WebSocket 端点和各种事件回调
 * @returns 返回连接状态、控制方法和工具函数
 */
export function useRealtime(config: Realtime.Config = {}) {
  const {
    wsEndpoint = '',
    enableInputAudioTranscription = true,
    autoConnect = true,
    maxRetries = 5,
    reconnectDelay = 3000,
    onWebSocketOpen,
    onWebSocketClose,
    onWebSocketError,
    onReceivedResponseAudioDelta,
    onReceivedInputAudioBufferSpeechStarted,
    onReceivedResponseDone,
    onReceivedResponseAudioTranscriptDelta,
    onReceivedResponseTextDelta,
    onReceivedResponseTextDone,
    onReceivedInputAudioTranscriptionCompleted,
    onReceivedError,
    onReceivedSessionCreated,
    onReceivedSessionUpdated,
    onReceivedInputAudioBufferSpeechStopped,
    onReceivedInputAudioBufferCommitted,
    onReceivedInputAudioBufferCleared,
    onReceivedConversationItemCreated,
    onReceivedResponseCreated,
    onReceivedResponseOutputItemAdded,
    onReceivedResponseContentPartAdded,
    onReceivedResponseContentPartDone,
    onReceivedResponseOutputItemDone,
    onReceivedResponseAudioDone,
    onReceivedResponseAudioTranscriptDone,
    onReceivedRateLimitsUpdated,
    onRunStarted,
    onRunFinished,
    onCustomEvent,
  } = config;

  // 创建WebSocket实例
  const { sendJsonMessage } = useWebSocket({
    wsEndpoint,
    autoConnect,
    maxRetries,
    reconnectDelay,
    onOpen: onWebSocketOpen,
    onClose: onWebSocketClose,
    onError: onWebSocketError,
    onMessage: (event: MessageEvent) => {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      handleMessage(event);
    },
  });

  /**
   * 处理从 WebSocket 接收到的消息
   * 根据消息类型分发到对应的回调函数
   *
   * @param event - WebSocket 消息事件
   */
  function handleMessage(event: MessageEvent) {
    let message: Realtime.Message;

    try {
      message = JSON.parse(event.data);
    } catch (error) {
      console.error('[Realtime] Failed to parse realtime message:', error);
      return;
    }

    switch (message.type) {
      // 会话相关
      // 会话创建
      case 'session.created':
        onReceivedSessionCreated?.(message as unknown as Realtime.SessionCreated);
        break;
      // 会话更新
      case 'session.updated':
        onReceivedSessionUpdated?.(message as unknown as Realtime.SessionUpdated);
        break;

      // 输入音频缓冲区相关
      // 用户开始说话
      case 'input_audio_buffer.speech_started':
        onReceivedInputAudioBufferSpeechStarted?.(message);
        break;
      // 用户停止说话
      case 'input_audio_buffer.speech_stopped':
        onReceivedInputAudioBufferSpeechStopped?.(message as unknown as Realtime.InputAudioBufferSpeechStopped);
        break;
      // 用户音频提交完成
      case 'input_audio_buffer.committed':
        onReceivedInputAudioBufferCommitted?.(message as unknown as Realtime.InputAudioBufferCommitted);
        break;
      // 用户音频缓冲区清空
      case 'input_audio_buffer.cleared':
        onReceivedInputAudioBufferCleared?.(message);
        break;

      // 对话项相关
      // 对话项创建
      case 'conversation.item.created':
        onReceivedConversationItemCreated?.(message as unknown as Realtime.ConversationItemCreated);
        break;
      // 用户语音转录完成
      case 'conversation.item.input_audio_transcription.completed':
        if ('transcript' in message) {
          onReceivedInputAudioTranscriptionCompleted?.(
            message as unknown as Realtime.ResponseInputAudioTranscriptionCompleted,
          );
        }
        break;

      // 响应相关
      // 响应创建
      case 'response.created':
        onReceivedResponseCreated?.(message as unknown as Realtime.ResponseCreated);
        break;
      // 响应输出项添加
      case 'response.output_item.added':
        onReceivedResponseOutputItemAdded?.(message as unknown as Realtime.ResponseOutputItemAdded);
        break;
      // 响应内容部分添加
      case 'response.content_part.added':
        onReceivedResponseContentPartAdded?.(message as unknown as Realtime.ResponseContentPartAdded);
        break;
      // 响应内容部分完成
      case 'response.content_part.done':
        onReceivedResponseContentPartDone?.(message as unknown as Realtime.ResponseContentPartDone);
        break;
      // 响应输出项完成
      case 'response.output_item.done':
        onReceivedResponseOutputItemDone?.(message as unknown as Realtime.ResponseOutputItemDone);
        break;
      // 响应音频增量
      case 'response.audio.delta':
        if ('delta' in message) {
          onReceivedResponseAudioDelta?.(message as unknown as Realtime.ResponseAudioDelta);
        }
        break;
      // 响应音频完成
      case 'response.audio.done':
        onReceivedResponseAudioDone?.(message as unknown as Realtime.ResponseAudioDone);
        break;
      // 响应音频转录增量
      case 'response.audio_transcript.delta':
        if ('delta' in message) {
          onReceivedResponseAudioTranscriptDelta?.(message as unknown as Realtime.ResponseAudioTranscriptDelta);
        }
        break;
      // 响应文本增量
      case 'response.text.delta':
        if ('delta' in message) {
          onReceivedResponseTextDelta?.(message as unknown as Realtime.ResponseTextDelta);
        }
        break;
      // 响应文本完成
      case 'response.text.done':
        if ('text' in message) {
          onReceivedResponseTextDone?.(message as unknown as Realtime.ResponseTextDone);
        }
        break;
      // 响应音频转录完成
      case 'response.audio_transcript.done':
        onReceivedResponseAudioTranscriptDone?.(message as unknown as Realtime.ResponseAudioTranscriptDone);
        break;
      // 响应完成
      case 'response.done':
        if ('response' in message) {
          onReceivedResponseDone?.(message as unknown as Realtime.ResponseDone);
        }
        break;

      // 工具调用相关
      case 'RUN_STARTED':
        onRunStarted?.(message as unknown as Realtime.RunStarted);
        break;
      // 工具调用完成
      case 'RUN_FINISHED':
        onRunFinished?.(message as unknown as Realtime.RunFinished);
        break;

      // 自定义事件
      case 'CUSTOM':
        onCustomEvent?.(message as unknown as Realtime.CustomEvent);
        break;

      // 速率限制相关
      // 速率限制更新
      case 'rate_limits.updated':
        onReceivedRateLimitsUpdated?.(message as unknown as Realtime.RateLimitsUpdated);
        break;

      // 错误处理
      case 'error':
        onReceivedError?.(message);
        break;

      default:
        console.log('[Realtime] Unhandled message:', message.type, message);
        native.logger.log(`未处理消息:${message.type}`);
        // 特别检查文本相关的消息
        if (message.type.includes('text') || message.type.includes('content')) {
          console.warn('[Realtime] 发现文本相关的未处理消息:', message);
        }
        // 特别检查是否是取消相关的消息
        if (message.type.includes('cancel')) {
          console.warn('[Realtime] 发现取消相关的未处理消息:', message);
        }
        // 检查是否是错误消息
        if (message.type === 'error') {
          console.error('[Realtime] 收到错误消息:', message);
          onReceivedError?.(message);
        }
        break;
    }
  }

  /**
   * 开始会话
   * 发送会话配置，启用服务器端语音活动检测和音频转录
   * 后端已默认发送
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function startSession(): boolean {
    const command: Realtime.SessionUpdateCommand = {
      type: 'session.update',
      session: {
        turn_detection: {
          type: 'server_vad',
        },
      },
    };

    if (enableInputAudioTranscription) {
      command.session.input_audio_transcription = {
        model: 'whisper-1',
      };
    }

    return sendJsonMessage(command);
  }

  /**
   * 更新会话配置
   *
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function updateSessionConfig(audio?: boolean) {
    const modalities = audio ? ['audio', 'text'] : ['text'];
    const command: Realtime.SessionUpdateCommand = {
      type: 'session.update',
      session: {
        modalities,
      },
    };

    return sendJsonMessage(command);
  }

  /**
   * 取消会话
   *
   * @param itemId - 消息ID
   * @param contentIndex - 内容索引
   * @param audioEndMs - 音频结束时间
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function conversationItemTruncate(itemId: string, contentIndex: number = 0, audioEndMs: number = 0): boolean {
    const command: Realtime.ConversationItemTruncateCommand = {
      type: 'conversation.item.truncate',
      item_id: itemId,
      content_index: contentIndex,
      audio_end_ms: audioEndMs,
    };

    return sendJsonMessage(command);
  }

  /**
   * 添加用户音频数据到输入缓冲区
   * 将 PCM 音频数据转换为 Base64 格式并发送到服务器
   *
   * @param pcmData - PCM 音频数据数组
   * @param sampleRate - 音频采样率
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function addUserAudio(base64Audio: string): boolean {
    // 转换为Base64格式
    const command: Realtime.InputAudioBufferAppendCommand = {
      type: 'input_audio_buffer.append',
      audio: base64Audio,
    };

    return sendJsonMessage(command);
  }

  /**
   * 清空音频输入缓冲区
   * 清除所有待处理的音频数据
   *
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function inputAudioBufferClear(): boolean {
    const command: Realtime.InputAudioBufferClearCommand = {
      type: 'input_audio_buffer.clear',
    };

    return sendJsonMessage(command);
  }

  /**
   * 发送文本消息
   * 创建对话项并触发 AI 响应生成
   *
   * @param text - 要发送的文本内容
   * @returns boolean - 发送成功返回 true，失败返回 false
   */
  function sendTextMessage(text: string): boolean {
    // 创建对话项
    const createItemCommand: Realtime.ConversationItemCreateCommand = {
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: RoleType.USER,
        content: [
          {
            type: 'input_text',
            text,
          },
        ],
      },
    };

    // 发送创建对话项命令
    const itemSuccess = sendJsonMessage(createItemCommand);
    if (!itemSuccess) {
      return false;
    }

    // 创建响应
    const createResponseCommand: Realtime.ResponseCreateCommand = {
      type: 'response.create',
    };

    // 发送创建响应命令
    return sendJsonMessage(createResponseCommand);
  }

  return {
    // 会话控制方法
    /** 开始会话 */
    startSession,
    /** 更新会话配置 */
    updateSessionConfig,
    /** 取消会话 */
    conversationItemTruncate,

    // 音频处理方法
    /** 添加用户音频数据到输入缓冲区 */
    addUserAudio,
    /** 清空音频输入缓冲区 */
    inputAudioBufferClear,

    // 消息发送方法
    /** 发送文本消息 */
    sendTextMessage,

    sendJsonMessage,
  };
}

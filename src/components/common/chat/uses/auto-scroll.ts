import { ref, onMounted, onUnmounted, type Ref } from 'vue';
import { throttle } from 'lodash';

/**
 * 是否可以下拉刷新
 *
 * @param contentInnerRef - 滚动容器引用
 * @returns boolean - 是否可以下拉刷新
 */
function getCanPullRefresh(contentInnerRef: Ref<HTMLElement | null>): boolean {
  const element = contentInnerRef.value;
  if (!element) return false;

  const { scrollTop, scrollHeight, clientHeight } = element;
  return scrollTop <= 5 || scrollHeight <= clientHeight;
}

export function useAutoScroll(contentInnerRef: Ref<HTMLElement | null>) {
  // 是否应该自动滚动（用户向上滚动后会设为false）
  const autoScroll = ref(true);
  // 是否滚动到顶部
  const isAtTop = ref(true);

  // 上次滚动位置
  let lastScrollTop = 0;
  // 历史记录加载前的滚动状态
  let beforeHistoryScrollHeight = 0;
  let beforeHistoryScrollTop = 0;

  // 滚动事件处理
  function handleScroll() {
    const element = contentInnerRef.value;
    if (!element) return;

    const { scrollTop, scrollHeight, clientHeight } = element;

    // 判断滚动方向
    const isScrollingUp = scrollTop < lastScrollTop;

    isAtTop.value = getCanPullRefresh(contentInnerRef);

    // 如果用户向上滚动，停止自动滚动
    if (isScrollingUp && scrollTop > 0) {
      autoScroll.value = false;
    }

    // 检查是否滚动到底部（允许5px的误差）
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;

    // 如果滚动到底部，重新启用自动滚动
    if (isAtBottom) {
      autoScroll.value = true;
    }

    lastScrollTop = scrollTop;
  }

  const throttledHandleScroll = throttle(handleScroll, 100);

  // 保存历史记录加载前的滚动状态
  function saveScrollStateBeforeHistory() {
    const element = contentInnerRef.value;
    if (!element) return;

    beforeHistoryScrollHeight = element.scrollHeight;
    beforeHistoryScrollTop = element.scrollTop;
  }

  // 恢复历史记录加载后的滚动位置
  function restoreScrollStateAfterHistory() {
    const element = contentInnerRef.value;
    if (!element) return;

    // 计算新增的内容高度
    const newContentHeight = element.scrollHeight - beforeHistoryScrollHeight;
    // 调整滚动位置，保持用户原来看到的内容位置不变
    const newScrollTop = beforeHistoryScrollTop + newContentHeight;

    element.scrollTop = newScrollTop;
    lastScrollTop = newScrollTop;
  }

  onMounted(() => {
    const element = contentInnerRef.value;
    if (element) {
      element.addEventListener('scroll', throttledHandleScroll, { passive: true });
      // 初始化滚动位置
      lastScrollTop = element.scrollTop;
      // 初始化下拉刷新状态
      isAtTop.value = getCanPullRefresh(contentInnerRef);
    }
  });

  onUnmounted(() => {
    const element = contentInnerRef.value;
    if (element) {
      element.removeEventListener('scroll', throttledHandleScroll);
    }
  });

  return {
    autoScroll,
    isAtTop,

    saveScrollStateBeforeHistory,
    restoreScrollStateAfterHistory,
  };
}

<script setup lang="ts">
import { toRef, useTemplateRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { onClickOutside } from '@vueuse/core';

import Textarea from '../textarea';
import ButtonAction from '../button-action';
import BottomActionArea from './bottom-action-area.vue';
import InputActionExpand from './input-action-expand.vue';

import { ChatMode } from '@/consts/chat-mode';
import { useInput } from './use';


const props = defineProps<{
  sending?: boolean;
}>();

const emits = defineEmits<{
  'on-switch-mode': [mode: ChatMode];
  'on-send': [value: string];
  'on-interrupt-ai-replying': [];
}>();

const { t } = useI18n();
const inputValue = defineModel<string>('modelValue', {
  default: '',
});
const chatInputRef = useTemplateRef<HTMLDivElement>('chatInputRef');
const textareaRef = useTemplateRef<typeof Textarea>('textareaRef');
const sending = toRef(props, 'sending');
const uit = useInput(textareaRef, sending);

function onSwitchMode(mode: ChatMode) {
  emits('on-switch-mode', mode);
}

function onSend() {
  uit.onCollapse();
  uit.onBlur();
  emits('on-send', inputValue.value);
}

function onInterruptAiReply() {
  emits('on-interrupt-ai-replying');
}

onClickOutside(chatInputRef, uit.onBlur);

</script>


<template>
  <div
    ref="chatInputRef"
    class="chat-input"
  >
    <div class="chat-input-inner">
      <ButtonAction
        v-show="!uit.isFocused.value"
        class="btn-phone"
        :width="56"
        :height="56"
        :img-width="34"
        :img-height="30"
        @click="onSwitchMode(ChatMode.SPEECH)"
      >
        <template #icon>
          <img src="@/assets/img/chat/icon-phone.svg">
        </template>
      </ButtonAction>

      <div
        class="input-wrap"
        :class="{
          focused: uit.isFocused.value,
          'single-line': uit.isSingleLine.value,
        }"
        @click="uit.onInputWrapClick"
      >
        <div
          class="input-wrap-content"
          :class="{
            sending,
          }"
        >
          <input
            v-show="uit.isSingleLine.value"
            v-model="inputValue"
            type="text"
            class="input-wrap-content-single-line"
            :disabled="sending"
            :placeholder="t('chat.footer.input.placeholder')"
            @focus="uit.onFocus"
          >

          <Textarea
            v-show="!uit.isSingleLine.value"
            ref="textareaRef"
            v-model="inputValue"
            :disabled="sending"
            :auto-size="uit.autoSize"
            :placeholder="t('chat.footer.input.placeholder')"
            @on-rows-change="uit.onRowsChange"
          />

          <InputActionExpand
            v-if="uit.isFocused.value"
            :is-collapsed="uit.isCollapsed.value"
            :is-show-expand-btn="uit.isShowExpandBtn.value"
            @on-expand="uit.onExpand"
            @on-collapse="uit.onCollapse"
          />
        </div>

        <BottomActionArea
          v-if="sending || uit.isFocused.value"
          :sending="sending"
          :block="uit.isFocused.value"
          @on-send="onSend"
          @on-stop="onInterruptAiReply"
        />
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.chat-input {
  padding: 22px 15px 16px;

  .chat-input-inner {
    display: flex;
    align-items: center;

    .btn-phone {
      margin-right: 6px;
    }

    .input-wrap {
      display: flex;
      flex: 1;
      align-items: center;
      padding: 13px 12px;
      background-color: #fff;
      border: 1px solid transparent;
      border-radius: 60px;

      .input-wrap-content {
        display: flex;
        width: 100%;

        .input-wrap-content-single-line {
          width: 100%;
          padding: 0;
          color: #111;
          font-size: 15px;
          font-family: inherit;
          line-height: 24px;
          background: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: #888;
          }
        }

        &.sending {
          padding-right: 12px;

          .input-wrap-content-single-line {
            color: #888;
          }
        }
      }

      &.single-line {
        box-sizing: border-box;
        height: 56px;
      }

      &.focused {
        flex-direction: column;
        padding: 12px;
        border-color: #0086ff;
        border-radius: 18px;
      }
    }
  }
}
</style>

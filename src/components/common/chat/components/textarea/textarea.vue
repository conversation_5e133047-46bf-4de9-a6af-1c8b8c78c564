<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useAutoSize, type AutoSizeConfig } from '../../uses/auto-size';

interface Props {
  disabled?: boolean;
  placeholder?: string; // 输入框占位符
  autoSize?: boolean | AutoSizeConfig; // 自动调整大小配置
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  autoSize: false,
});

interface Emits {
  'update:model-value': [],
  'on-focus': [],
  'on-rows-change': [rows: number],
}

const emits = defineEmits<Emits>();

const inputValue = defineModel<string>();
const textareaRef = ref<HTMLTextAreaElement>();
const currentRows = ref(1);

let autoSizeCleanup: (() => void) | null = null;

// 自动调整大小
function autoSizeIfNeeded() {
  if (props.autoSize) {
    const { autoResize, cleanup } = useAutoSize(
      textareaRef.value!,
    );

    // 保存清理函数
    autoSizeCleanup = cleanup;

    // 执行自动调整大小
    const performAutoResize = () => {
      const autoSizeConfig = typeof props.autoSize === 'boolean' ? {} : props.autoSize;
      nextTick(() => {
        const { currentRows: newRows } = autoResize(autoSizeConfig);

        if (newRows !== currentRows.value) {
          currentRows.value = newRows;
          emits('on-rows-change', newRows);
        }
      });
    };

    // 监听 modelValue 变化
    watch([inputValue, props.autoSize], () => {
      performAutoResize();
    }, {
      immediate: true,
    });
  }
}

// 获取焦点
function focus() {
  textareaRef.value?.focus();
}

// 失去焦点
function blur() {
  textareaRef.value?.blur();
}

// 获取当前行数
function getCurrentRows() {
  return currentRows.value;
}

// 滚动至底部
function scrollToBottom() {
  if (textareaRef.value) {
    if (textareaRef.value.scrollTop === textareaRef.value.scrollHeight) {
      return;
    }

    textareaRef.value.scrollTop = textareaRef.value.scrollHeight;
  }
}

// 滚动至顶部
function scrollToTop() {
  if (textareaRef.value) {
    if (textareaRef.value.scrollTop === 0) {
      return;
    }

    textareaRef.value.scrollTop = 0;
  }
}

defineExpose({
  focus,
  blur,
  getCurrentRows,
  scrollToBottom,
  scrollToTop,
});

onMounted(() => {
  autoSizeIfNeeded();
});

onUnmounted(() => {
  if (autoSizeCleanup) {
    autoSizeCleanup();
  }
});
</script>


<template>
  <textarea
    ref="textareaRef"
    v-model="inputValue"
    v-bind="$attrs"
    class="input-area"
    :placeholder="placeholder"
    :disabled="disabled"
  />
</template>


<style scoped>
.input-area {
  width: 100%;
  height: 24px;
  padding: 0;
  font-size: 15px;
  line-height: 24px;
  border: none;
  outline: none;
  box-shadow: none;
  resize: none;

  &::placeholder {
    color: #888;
  }
}
</style>

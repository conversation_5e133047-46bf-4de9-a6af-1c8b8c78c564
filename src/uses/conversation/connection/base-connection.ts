/**
 * 连接层基础抽象类
 * 定义通用的连接管理接口，支持不同大模型提供商的实现
 */

import { ref, type Ref } from 'vue';

/**
 * 基础连接抽象类
 * 所有大模型提供商的连接实现都应该继承此类
 */
export abstract class BaseConnection {
  /** 连接状态 */
  public readonly state: Ref<Conversation.ConnectionState> = ref(Conversation.ConnectionState.DISCONNECTED);
  
  /** 连接配置 */
  protected config: Conversation.ConnectionConfig;
  
  /** 事件监听器映射 */
  protected eventListeners: Map<string, Set<(...args: unknown[]) => void>> = new Map();
  
  /** 重试计数器 */
  protected retryCount = 0;
  
  /** 重连定时器 */
  protected reconnectTimer: NodeJS.Timeout | null = null;

  constructor(config: Conversation.ConnectionConfig) {
    this.config = config;
  }

  /**
   * 连接到服务
   */
  abstract connect(): Promise<void>;

  /**
   * 断开连接
   */
  abstract disconnect(): Promise<void>;

  /**
   * 发送消息
   */
  abstract sendMessage(message: unknown): Promise<boolean>;

  /**
   * 获取连接状态
   */
  getState(): Conversation.ConnectionState {
    return this.state.value;
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.state.value === Conversation.ConnectionState.CONNECTED;
  }

  /**
   * 注册事件监听器
   */
  on(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: (...args: unknown[]) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * 触发事件
   */
  protected emit(event: string, ...args: unknown[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`[Connection] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 更新连接状态
   */
  protected setState(newState: Conversation.ConnectionState): void {
    const oldState = this.state.value;
    this.state.value = newState;
    this.emit('stateChange', newState, oldState);
  }

  /**
   * 处理连接错误
   */
  protected handleError(error: Error): void {
    console.error('[Connection] Error:', error);
    this.setState(Conversation.ConnectionState.ERROR);
    this.emit('error', error);
    
    // 如果配置了自动重连，则尝试重连
    if (this.shouldRetry()) {
      this.scheduleReconnect();
    }
  }

  /**
   * 是否应该重试连接
   */
  protected shouldRetry(): boolean {
    return this.retryCount < (this.config.maxRetries || 5);
  }

  /**
   * 安排重连
   */
  protected scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const delay = this.config.reconnectDelay || 3000;
    this.setState(Conversation.ConnectionState.RECONNECTING);
    
    this.reconnectTimer = setTimeout(async () => {
      this.retryCount++;
      try {
        await this.connect();
      } catch (error) {
        this.handleError(error as Error);
      }
    }, delay);
  }

  /**
   * 重置重试计数器
   */
  protected resetRetryCount(): void {
    this.retryCount = 0;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    this.eventListeners.clear();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    this.resetRetryCount();
  }

  /**
   * 销毁连接实例
   */
  async destroy(): Promise<void> {
    await this.disconnect();
    this.cleanup();
  }
}

/**
 * 连接工厂接口
 */
export interface ConnectionFactory {
  /**
   * 创建连接实例
   */
  createConnection(config: Conversation.ConnectionConfig): BaseConnection;
  
  /**
   * 获取支持的协议类型
   */
  getSupportedProtocols(): string[];
}

/**
 * 连接管理器
 * 负责管理不同类型的连接实例
 */
export class ConnectionManager {
  private connections: Map<string, BaseConnection> = new Map();
  private factories: Map<string, ConnectionFactory> = new Map();

  /**
   * 注册连接工厂
   */
  registerFactory(protocol: string, factory: ConnectionFactory): void {
    this.factories.set(protocol, factory);
  }

  /**
   * 创建连接
   */
  createConnection(protocol: string, config: Conversation.ConnectionConfig): BaseConnection {
    const factory = this.factories.get(protocol);
    if (!factory) {
      throw new Error(`Unsupported protocol: ${protocol}`);
    }

    const connectionId = `${protocol}_${Date.now()}`;
    const connection = factory.createConnection(config);
    this.connections.set(connectionId, connection);
    
    return connection;
  }

  /**
   * 获取连接
   */
  getConnection(connectionId: string): BaseConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * 移除连接
   */
  async removeConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      await connection.destroy();
      this.connections.delete(connectionId);
    }
  }

  /**
   * 清理所有连接
   */
  async cleanup(): Promise<void> {
    const promises = Array.from(this.connections.values()).map(conn => conn.destroy());
    await Promise.all(promises);
    this.connections.clear();
  }
}

// 全局连接管理器实例
export const connectionManager = new ConnectionManager();

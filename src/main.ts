import 'core-js/stable';
import 'amfe-flexible';
import { Component, createApp } from 'vue';
import type { Composer } from 'vue-i18n';
import { createPinia } from 'pinia';

import { ErrorHandlerPlugin } from '@/plugins/error-handler';
import { VantPlugin } from '@/plugins/vant';

import { NativeEvent } from '@/config/native-event';
import { i18n } from '@/i18n';
import eventBus from '@/utils/event-bus';
import { createErudaIfNeed } from '@/utils/create-eruda-if-need';

// 基础样式
import 'vant/lib/index.css';
import '@/styles/chat.less'; // 代码高亮样式
import '@/styles/reset.less';
import '@/styles/app.less';
import '@/styles/utils.less';


function useCreateApp(component: Component) {
  const app = createApp(component);

  function handleAccountError() {
    native.events.dispatchEvent('accesstokenexpries');
  }

  eventBus.on('accessTokenInvalidError', handleAccountError);
  eventBus.on('refreshTokenInvalidError', handleAccountError);
  eventBus.on('accountDisabledError', handleAccountError);

  (i18n.global as unknown as Composer).locale.value = window.native.i18n.language;

  app.use(i18n);
  app.use(ErrorHandlerPlugin);
  app.use(VantPlugin, { i18n });
  app.use(createPinia());
  app.mount('#app');
}

export function createAppWhenNativeReady(component: Component) {
  /**
   * 创建调试工具
   * 在env文件中配置
  */
  createErudaIfNeed();

  window.addEventListener(NativeEvent.NATIVE_READY, () => {
    // iOS下设置界面不能滚动，Android不可设置（设置导致页面无法正常滚动）
    if (/(iPhone|iPad|iPod|iOS)/i.test(window.navigator.userAgent)) {
      window.native.navigator.setScrollEnabled(false);
    }

    useCreateApp(component);
  });

  window.clientready = true;
}

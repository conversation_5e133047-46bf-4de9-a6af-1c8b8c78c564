import _ from 'lodash';

export interface PaginationParamsOption extends Record<string, unknown> {
  page: {
    index: number,
    size: number,
  },
}

export interface PaginationResult extends Record<string, unknown> {
  offsetStart: number,
  maxPageItems: number,
  [key: string]: unknown,
}

export function paginationParams(params: PaginationParamsOption): PaginationResult {
  return {
    ..._.cloneDeep(_.omit(params, ['page'])),
    offsetStart: _.get(params, 'page.index', 1),
    maxPageItems: _.get(params, 'page.size', 15),
  } as PaginationResult;
}

@import './reset.less';
@import './vant.less';


body, html {
  box-sizing: border-box;
  word-break: break-word;
  font-family: "Microsoft YaHei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Arial Unicode MS", Mingliu, Arial, Helvetica;
  height: 100%;
  background-color: #f7f7f7;
}

#app {
  height: 100%;
  font-size: 14px;
}

.page-container {
  position: relative;
  height: 100%;
}

.dialog {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .dialog-wrapper {
    width: 100%;
    background-color: @white;
    box-sizing: border-box;
    border-radius: 5px;
    padding: 25px 30px;
    margin: 0 38px;

    .dialog-title {
      line-height: 25px;
      font-size: 16px;
      color: #222222;
      text-align: center;
    }

    .dialog-action {
      margin-top: 32px;
    }
  }
}

import { ref, reactive, type Ref, nextTick, computed, watch } from 'vue';
import { useKeyboard } from '@/uses/keyboard';
import type Textarea from '../textarea';

export function useInput(textareaRef: Ref<typeof Textarea | null>, sending: Ref<boolean>) {
  const keyboard = useKeyboard();
  const isFocused = ref(false);
  const isShowExpandBtn = ref(false);
  const isCollapsed = ref(true);
  const autoSize = reactive({ minRows: 1, maxRows: 1 });

  const isSingleLine = computed(() => {
    return autoSize.maxRows === 1;
  });

  // 获取焦点
  function onFocus() {
    isFocused.value = true;
    autoSize.maxRows = 3;
    nextTick(() => {
      textareaRef.value?.focus();
    });
  }

  // 失去焦点
  function onBlur() {
    textareaRef.value?.blur();
    isFocused.value = false;
    isCollapsed.value = true;
    isShowExpandBtn.value = false;
    autoSize.minRows = 1;
    autoSize.maxRows = 1;
  }

  // 行数变化
  function onRowsChange(rows: number) {
    isShowExpandBtn.value = rows >= 3;
  }

  // 全屏显示输入框
  function onExpand() {
    if (!textareaRef.value) {
      return;
    }

    isCollapsed.value = false;

    const textareaElement = textareaRef.value.$el;
    const remainingHeight = textareaElement.getBoundingClientRect().top;
    // 减去两行，保留安全距离，避免组件抖动
    const newRows = Math.floor(remainingHeight / 24) + textareaRef.value.getCurrentRows() - 2;

    autoSize.minRows = newRows;
    autoSize.maxRows = newRows;
  }

  // 收起输入框
  function onCollapse() {
    isCollapsed.value = true;
    autoSize.minRows = 1;
    autoSize.maxRows = 3;

    requestAnimationFrame(() => {
      textareaRef.value?.scrollToBottom();
    });
  }

  function onInputWrapClick() {
    if (!isFocused.value) {
      if (!sending.value) {
        onFocus();
      }
    } else if (!isSingleLine.value) {
      textareaRef.value?.focus();
    }
  }

  // 监听键盘状态变化，键盘收起时回到单行状态
  watch(keyboard.isKeyboardVisible, (isVisible, wasVisible) => {
    // 当键盘从显示状态变为隐藏状态时
    if (wasVisible && !isVisible && isFocused.value) {
      onBlur();
    }
  });

  return {
    isFocused,
    isShowExpandBtn,
    isCollapsed,
    autoSize,
    isSingleLine,

    onFocus,
    onBlur,
    onRowsChange,
    onExpand,
    onCollapse,
    onInputWrapClick,
  };
}

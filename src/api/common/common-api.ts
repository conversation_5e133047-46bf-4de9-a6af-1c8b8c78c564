import { API_SALT } from '@/config/api';
import {
  type RequestConfig,
  type RequestMethod,
  type RequestParams,
  type RequestData,
  BaseRequestApi,
} from '../base/base-request-api';

export { type RequestConfig, type RequestMethod, type RequestParams, type RequestData };

export class Common<PERSON>pi<T> extends BaseRequestApi<T> {
  constructor(args?: RequestConfig) {
    super({
      baseURL: process.env.AI_MARY_API_BASE_URL,
      salt: API_SALT,
      ...args,
    });
  }
}

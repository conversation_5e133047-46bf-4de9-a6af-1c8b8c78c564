<script setup lang="ts">
defineProps<{
  isShowExpandBtn: boolean;
  isCollapsed: boolean;
}>();

const emits = defineEmits<{
  'on-expand': [];
  'on-collapse': [];
}>();
</script>


<template>
  <div class="input-action-expand">
    <template v-if="isShowExpandBtn">
      <ButtonAction
        v-show="isCollapsed"
        :width="34"
        :height="34"
        :img-width="18"
        :img-height="18"
        @mousedown.prevent
        @click.stop="emits('on-expand')"
      >
        <template #icon>
          <img src="@/assets/img/chat/icon-expand.svg">
        </template>
      </ButtonAction>

      <ButtonAction
        v-show="!isCollapsed"
        :width="34"
        :height="34"
        :img-width="18"
        :img-height="18"
        @mousedown.prevent
        @click.stop="emits('on-collapse')"
      >
        <template #icon>
          <img src="@/assets/img/chat/icon-collapse.svg">
        </template>
      </ButtonAction>
    </template>
  </div>
</template>


<style scoped lang="less">
.input-action-expand {
  width: 34px;
  height: 100%;
  margin-left: 8px;

  .btn-action {
    padding: 8px;
    cursor: pointer;
  }
}
</style>

import { AxiosRequestConfig, AxiosResponse } from 'axios';
import URI from 'urijs';


interface IErrorEventTarget extends EventTarget {
  status: string;
}

interface IErrorEvent extends Event {
  target: IErrorEventTarget;
}

interface ILoadEventTarget extends EventTarget {
  responseText: string;
}

interface ILoadEvent extends Event {
  target: ILoadEventTarget;
}

function isFormData(thing: unknown) {
  if (!thing) {
    return false;
  }

  if (typeof FormData === 'function' && thing instanceof FormData) {
    return true;
  }

  return Object.prototype.toString.call(thing) === '[object FormData]';
}

export function nativeAdapter<T>(config: AxiosRequestConfig) {
  return new Promise<AxiosResponse<T>>((resolve, reject) => {
    let requestData = config.data;
    const requestHeaders = config.headers ?? {};

    if (isFormData(requestData)) {
      delete requestHeaders['Content-Type']; // Let the browser set it
    }

    let url;
    if (config.baseURL && !config.baseURL.endsWith('/')) {
      url = `${config.baseURL}/${config.url}`;
    } else if (config.baseURL) {
      url = `${config.baseURL}${config.url}`;
    } else {
      url = config.url;
    }

    let request = new native.net.XMLHttpRequest();
    request.timeout = config.timeout;

    const uri = new URI(url);
    uri.addSearch(config.params);
    request.open(
      config.method?.toUpperCase(),
      uri.href(),
    );

    request.onreadystatechange = () => {
      // native.logger.log('net,onreadystatechange ');
    };
    request.onloadstart = () => {
      // native.logger.log('net,onloadstart');
    };
    request.onprogress = () => {
      // native.logger.log('net,onprogress');
    };
    request.onabort = () => {
      // native.logger.log('net,onabort');
    };

    request.onerror = (ev: IErrorEvent) => {
      // native.logger.log('net,onerror');
      const error = new Error();
      Object.assign(error, {
        code: `STATUS_${ev.target.status}`,
      });
      reject(error);
      // Clean up request
      request = null;
    };

    // Listen for ready state
    const cb = function handleLoad(ev: ILoadEvent) {
      // native.logger.log('net,onload');
      // Prepare the response
      try {
        const headers = request.getAllResponseHeaders();
        const data = JSON.parse(ev.target.responseText);
        const response = {
          status: request.status,
          statusText: request.statusText,
          headers,
          config,
          request,
          data,
        } as AxiosResponse<T>;
        resolve(response);
      } catch (error) {
        reject(error);
      }

      // Clean up request
      request = null;
    };
    request.onload = cb;

    request.ontimeout = () => {
      const error = new Error();
      Object.assign(error, {
        code: 'NETWORK_TIMEOUT',
      });
      reject(error);
      request = null;
    };

    request.onloadend = () => {
      // native.logger.log('net,onloadend ');
    };

    // Add headers to the request
    if ('setRequestHeader' in request) {
      Object.entries(requestHeaders).forEach(([key, val]) => {
        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {
          // Remove Content-Type if data is undefined
          Object.assign(requestHeaders, {
            [key]: undefined,
          });
        } else {
          // Otherwise add header to the request
          request.setRequestHeader(key, val);
        }
      });
    }

    if (requestData === undefined) {
      requestData = null;
    }

    // Send the request
    request.send(requestData);
  });
}

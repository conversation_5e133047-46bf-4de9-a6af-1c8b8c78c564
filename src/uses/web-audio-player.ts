import { usePlayer } from './player';

export function useAudioPlayer(sampleRate: number = 24000) {
  let audioPlayer: ReturnType<typeof usePlayer> | null = null;

  function reset() {
    audioPlayer = usePlayer();
    audioPlayer.init(sampleRate);
  }

  function play(base64Audio: string) {
    const binary = atob(base64Audio);
    const bytes = Uint8Array.from(binary, (c) => c.charCodeAt(0));
    const pcmData = new Int16Array(bytes.buffer);

    audioPlayer?.play(pcmData);
  }

  function stop() {
    audioPlayer?.stop();
  }

  return {
    reset,
    play,
    stop,
  };
}

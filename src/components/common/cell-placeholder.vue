<script setup lang="ts">
import { computed } from 'vue';
import { isNothing } from '@/utils/is';

interface Props {
  modelValue: string | number | null | undefined;
  placeholderText?: string | undefined;
  attrLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  placeholderText: '--',
  attrLabel: 'span',
});

const text = computed(() => {
  return isNothing(props.modelValue) ? props.placeholderText : props.modelValue;
});
</script>


<template>
  <component
    :is="attrLabel"
    class="cell-placeholder"
    v-bind="$attrs"
  >
    {{ text }}
  </component>
</template>

<script setup lang="ts">

</script>


<template>
  <div class="chat-message-loading">
    <span
      v-for="i in 3"
      :key="i"
      class="loading-dot"
      :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
    />
  </div>
</template>


<style scoped lang="less">
@keyframes loading {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.3;
  }
}

.chat-message-loading {
  background-color: transparent;

  .loading-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 4px;
    background-color: #0086ff;
    border-radius: 50%;
    animation: loading 1s infinite ease-in-out;
  }
}
</style>

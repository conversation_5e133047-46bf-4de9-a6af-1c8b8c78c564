const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const Dotenv = require('dotenv-webpack');
const CopyPlugin = require('copy-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const { VantResolver } = require('@vant/auto-import-resolver');
const AutoImport = require('unplugin-auto-import/webpack');
const Components = require('unplugin-vue-components/webpack');
require('./env-config');
const utils = require('./utils');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

const dotenvFilePath = () => {
  const f = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';
  return resolve('../', f);
};

module.exports = () => {
  return {
    target: ['web', 'es5'],
    output: {
      publicPath: (process.env.NODE_ENV === 'production' ? './' : '/'),
      path: resolve('../dist'), // 出口文件路径
      clean: true,
      filename: ({ chunk }) => {
        if (chunk.name === 'app') {
          return '[name].js';
        }

        return 'static/js/[name].[chunkhash].js';
      },
      chunkFilename: 'static/js/[name].[chunkhash].js',
    },
    entry: utils.createEntries(),
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: [
            'thread-loader',
            {
              loader: 'esbuild-loader',
              options: {
                loader: 'js',
                target: 'es2015', // 设置编译目标，默认是 esnext
              },
            },
          ],
        },
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: [
            'thread-loader',
            {
              loader: 'esbuild-loader',
              options: {
                loader: 'ts', // 使用 TypeScript loader
                target: 'es2015',
              },
            },
          ],
        },
        {
          test: /\.vue$/,
          loader: 'vue-loader', // vue文件编译
        },
        {
          test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 512 * 1024,
            },
          },
          generator: {
            filename: 'static/assets/[name].[ext]',
          },
        },
        {
          test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 10 * 1024,
            },
          },
          generator: {
            filename: 'static/fonts/[name].[ext]',
          },
        },
        {
          test: /\.css$/,
          use: [
            MiniCssExtractPlugin.loader,
            'css-loader',
            'postcss-loader',
          ],
        },
        {
          test: /\.less$/,
          use: [
            MiniCssExtractPlugin.loader,
            'css-loader',
            'postcss-loader',
            {
              loader: 'less-loader',
              options: {
                lessOptions: {
                  javascriptEnabled: true,
                },
              },
            },
          ],
        },
      ],
    },
    plugins: [
      new NodePolyfillPlugin(),
      AutoImport.default({
        resolvers: [VantResolver()],
      }),
      Components.default({
        resolvers: [VantResolver()],
      }),
      new VueLoaderPlugin(),
      new Dotenv({
        path: dotenvFilePath(),
      }),
      ...utils.createHtmlWebpackPlugin(),
      new MiniCssExtractPlugin({
        filename: 'static/css/[name].[contenthash].css',
        chunkFilename: 'static/css/[name].[contenthash].css',
        ignoreOrder: true,
      }),
      new CopyPlugin({
        patterns: [
          {
            from: resolve('../public/'),
            to: resolve('../dist/static/'),
          },
        ],
      }),
    ],
    resolve: {
      extensions: ['.js', '.ts', '.vue', '.less', '.mjs'], // 引入省略后缀，LESS为部分依赖库会不使用.less后缀
      alias: {
        '@': resolve('../src'),
        '~': resolve('../node_modules'),
      },
    },
    externals: {
      WmChatLib: 'WmChatLib',
    },
    performance: {
      hints: false,
    },
  };
};

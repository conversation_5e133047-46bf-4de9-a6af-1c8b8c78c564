<script setup lang="ts" name="ToastError">
import { ref, onMounted } from 'vue';

const props = withDefaults(defineProps<{
  duration?: number,
  title?: string,
  message?: string,
}>(), {
  duration: 0,
  title: undefined,
  message: undefined,
});

type Emits = (e: 'hide') => void;

const emit = defineEmits<Emits>();

const model = ref(true);


onMounted(() => {
  setTimeout(() => {
    model.value = false;
    emit('hide');
  }, props.duration);
});
</script>


<template>
  <VanPopup
    v-model:show="model"
    class="toast-error-popup"
  >
    <div class="toast-error-wrap">
      <div class="body">
        <img
          class="ico-error"
          src="@/assets/img/icon-fail.png"
        >
        <div class="title">
          {{ $props.title }}
        </div>
        <div
          v-if="$props.message"
          class="message"
        >
          {{ $props.message }}
        </div>
      </div>
    </div>
  </VanPopup>
</template>


<style lang="less">
.van-popup.toast-error-popup {
  background-color: transparent;
}

.toast-error-wrap {
  box-sizing: border-box;
  width: calc(100vw - var(--van-padding-md) * 2);
  padding: 0 38px;

  .body {
    padding: 46px 20px;
    color: #222;
    font-weight: 400;
    font-size: 17px;
    text-align: center;
    background-color: #fff;
    border-radius: 5px;

    .ico-error {
      width: 35px;
    }

    .title {
      margin-top: 10px;
    }

    .message {
      margin-top: 10px;
      color: #555;
      font-size: 15px;
    }
  }
}
</style>

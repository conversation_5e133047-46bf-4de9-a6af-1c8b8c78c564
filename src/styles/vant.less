@white: #fff;
@red: #ff5c5c;
@blue: #4476d5;
@green: #4caf4b;
@orange: #eb6c00;

@button-default-color: #222;
@button-primary-background-color: #4476d5;
@button-primary-border-color: #4476d5;
@checkbox-checked-icon-color: #4476d5;
@radio-checked-icon-color: #4476d5;
@search-padding: 6px 16px;
@search-content-background-color: #f4f4f4;
@search-input-height: 32px;
@search-label-color: #888;
@search-label-font-size: 15px;
@tab-text-color: #555;
@tab-active-text-color: #4476d5;
@tab-font-size: 17px;
@tabs-default-color: #4476d5;
@tabs-line-height: 56px;
@tabs-bottom-bar-height: 1px;
@cell-font-size: 16px;
// 自定义
@primary: #4476d5;
@background-color: #f4f4f4;
@highlight-background-color: #e8edf7;


.wm {
  &.van-form {
    .van-field {
      position: relative;
      padding: 18px 20px;
      color: #222;
      font-weight: 400;
      font-size: 16px;

      &::after {
        right: 0;
        left: 0;
        border-bottom-color: #ededed;
      }

      .van-field__value {
        .van-field__control.van-field__control--custom {
          .placeholder {
            color: #888;
          }
        }

        input.van-field__control::placeholder {
          color: #888;
        }

        textarea.van-field__control::placeholder {
          color: #888;
        }

        .van-field__error-message {
          color: #ff5c5c;
          font-size: 14px;
        }

        .van-field__word-limit {
          color: #888;
        }
      }

      .van-icon.van-icon-arrow.van-cell__right-icon {
        width: 10px;
        height: 20px;
        margin-left: 10px;
        line-height: 20px;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: url("~@/assets/img/icon-jump-go.png") no-repeat center center / 100% auto;
          content: " ";
        }
      }

      &.van-field--error {
        .van-field__control {
          color: #ff5c5c;
        }
      
        .van-field__control::placeholder {
          color: #ff5c5c;
        }
      }
    }
  }

  &.van-cell-group {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    &.van-hairline--top-bottom {
      &::after {
        border-bottom-color: #ededed;
      }
    }

    .van-cell {
      padding: 18px 20px;
      font-weight: 400;
      font-size: 16px;

      &::after {
        right: 0;
        left: 0;
        border-bottom-color: #ededed;
      }
  
      .van-cell__title {
        color: #222;

        .van-cell__label {
          margin-top: 10px;
          color: #555;
          font-weight: 400;
          font-size: 16px;
        }
      }

      .van-cell__value {
        color: #555;
      }

      &.van-cell--clickable {
        .van-icon.van-icon-arrow.van-cell__right-icon {
          width: 10px;
          height: 20px;
          margin-left: 10px;
          line-height: 20px;
  
          &::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("~@/assets/img/icon-jump-go.png") no-repeat center center / 100% auto;
            content: " ";
          }
        }
      }
    }
  }

  &.van-button {
    font-weight: 400;

    &.van-button--primary {
      background-color: #365aa4;
      border-color: #365aa4;
    }

    &.van-button--default {
      color: #000;
      border-color: #ededed;
    }

    &.van-button--large {
      font-size: 17px;
      border-radius: 3px;
    }

    &.van-button--normal {
      font-size: 15px;
      border-radius: 2px;
    }
  }

  &.van-search {
    padding: 6px 16px;

    &.van-search--show-action {
      padding-right: 0;
    }

    .van-search__content {
      padding-left: 10px;
      background-color: #f4f4f4;

      &.van-search__content--round {
        border-radius: 5px;
      }

      .van-field__left-icon {
        .van-icon-search {
          width: 16px;
          height: 24px;
    
          &::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("~@/assets/img/icon-search.png") no-repeat center center / 100% auto;
            content: " ";
          }
        }
      }

      .van-field__body {
        font-weight: 400;
        font-size: 15px;

        .van-field__control::placeholder {
          color: #888;
        }
      }

      .van-search__action {
        padding: 0 20px;
        color: #222;
        font-weight: 400;
        font-size: 15px;
      }
    }
  }
}

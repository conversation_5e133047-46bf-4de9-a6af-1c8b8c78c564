const { merge } = require('webpack-merge');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const getBaseConfig = require('./webpack.base');


module.exports = (env) => {
  const config = merge(getBaseConfig(env), {
    mode: 'production',
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: false,
            },
          },
          extractComments: false,
        }),
        new CssMinimizerPlugin(),
      ],
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: 7,
        cacheGroups: {
          commons: {
            test: (module) => /src/.test(module.context) && !/\.css$/.test(module.request),
            minChunks: 2,
            name: 'commons',
            priority: 1,
          },
          vendors: {
            test: (module) => /node_modules/.test(module.context)
              && !/node_modules\/vant\/dist/.test(module.context)
              && !/\.css$/.test(module.request),
            name: 'vendors',
            chunks: 'initial',
            priority: 2,
          },
          vue: {
            test: (module) => /node_modules\/vue\//.test(module.context) && !/\.css$/.test(module.request),
            name: 'vue',
            chunks: 'initial',
            priority: 3,
          },
          'core-js': {
            test: (module) => /node_modules\/core-js\//.test(module.context) && !/\.css$/.test(module.request),
            name: 'core-js',
            chunks: 'initial',
            priority: 4,
          },
          lodash: {
            test: (module) => /node_modules\/lodash/.test(module.context) && !/\.css$/.test(module.request),
            name: 'lodash',
            chunks: 'initial',
            priority: 5,
          },
          'date-fns': {
            test: (module) => /node_modules\/date-fns\//.test(module.context) && !/\.css$/.test(module.request),
            name: 'date-fns',
            chunks: 'initial',
            priority: 6,
          },
          vant: {
            test: (module) => /node_modules\/vant\//.test(module.context) && !/\.css$/.test(module.request),
            name: 'vant',
            chunks: 'initial',
            priority: 7,
          },
        },
      },
      runtimeChunk: {
        name: 'manifest',
      },
    },
  });

  if (env.report) {
    var BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
    config.plugins.push(new BundleAnalyzerPlugin());
  }

  return config;
};

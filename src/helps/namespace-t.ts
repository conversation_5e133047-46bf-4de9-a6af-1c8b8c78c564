import type { Composer } from 'vue-i18n';
import _ from 'lodash';
import { i18n } from '@/i18n';

type I18nFn = (key: string, ...args: unknown[]) => string;

export function namespaceT(namespace?: string, t: I18nFn = (i18n.global as unknown as Composer).t): I18nFn {
  return (q: string, ...args: unknown[]) => {
    if (!_.isNil(namespace)) {
      return t(`${namespace}.${q}`, ...args) as string;
    }

    return t(`${q}`, ...args) as string;
  };
}

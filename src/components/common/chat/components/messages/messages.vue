<script setup lang="ts">
import { useTemplateRef, watch } from 'vue';
import Message from '../message';

import { Align } from '@/consts/align';


const props = withDefaults(
  defineProps<{
    messages?: Chat.Message[];
    align?: Align;
    autoScroll?: boolean;
  }>(),
  {
    autoScroll: true,
    align: Align.LEFT_RIGHT,
    messages: () => [],
  },
);

const emits = defineEmits<{
  'on-content-change': [content: string];
}>();

const messagesRef = useTemplateRef<HTMLElement>('messagesRef');

function scrollToBottom(behavior: ScrollBehavior = 'instant') {
  requestAnimationFrame(() => {
    if (!messagesRef.value) {
      return;
    }

    messagesRef.value.scrollIntoView({
      behavior,
      block: 'end',
    });
  });
}

function scrollToBottomIfNeeded() {
  if (props.autoScroll) {
    scrollToBottom();
  }
}

function onContentChange(content: string) {
  emits('on-content-change', content);

  scrollToBottomIfNeeded();
}

watch(() => props.messages.length, scrollToBottomIfNeeded, {
  immediate: true,
});
</script>


<template>
  <div
    ref="messagesRef"
    class="chat-messages"
  >
    <Message
      v-for="message in messages"
      :key="message.id"
      :message="message"
      :align="align"
      @on-content-change="onContentChange"
    />
  </div>
</template>


<style scoped lang="less">
.chat-messages {
  box-sizing: border-box;
  width: 100%;
  padding: 24px 16px;
}
</style>

<script setup lang="ts">
import { computed } from 'vue';
import { Button } from 'vant';

const props = withDefaults(
  defineProps<{
    height?: number;
    width?: number;
    imgWidth?: number;
    imgHeight?: number;
  }>(),
  {
    height: 66,
    width: 66,
    imgWidth: 16,
    imgHeight: 16,
  },
);

const width = computed(() => `${props.width}px`);
const height = computed(() => `${props.height}px`);
const imgWidth = computed(() => `${props.imgWidth}px`);
const imgHeight = computed(() => `${props.imgHeight}px`);
</script>


<template>
  <Button
    class="chat-button-action"
    v-bind="$attrs"
  >
    <template #icon>
      <slot name="icon" />
    </template>
  </Button>
</template>


<style scoped lang="less">
.chat-button-action {
  width: v-bind(width);
  height: v-bind(height);
  border: none;
  border-radius: 50%;

  :deep(.van-button__icon) {
    /* stylelint-disable-next-line value-keyword-case */
    width: v-bind(imgWidth);
    /* stylelint-disable-next-line value-keyword-case */
    height: v-bind(imgHeight);
  }
}
</style>

<script setup lang="ts">
import { ref, watch, type Component } from 'vue';
import { evaluate, type UseMdxComponents } from '@mdx-js/mdx';
import * as runtime from 'vue/jsx-runtime';
// eslint-disable-next-line import/no-extraneous-dependencies
import rehypeHighlight from 'rehype-highlight';
// eslint-disable-next-line import/no-extraneous-dependencies
import rehypeExternalLinks from 'rehype-external-links';

const props = defineProps<{
  /** MDX 内容 */
  content: string;
  /** 可用的组件映射 */
  components?: UseMdxComponents;
}>();

const mdxComponent = ref<Component>();

// 编译 MDX 内容
async function compileMDX() {
  const mdxModule = await evaluate(props.content, {
    ...runtime,
    rehypePlugins: [
      // 外部链接处理 - 需要在代码高亮之前处理
      [rehypeExternalLinks, {
        target: '_blank',
        rel: ['noopener', 'noreferrer'],
      }],
      // 代码高亮
      rehypeHighlight,
    ],
    useMDXComponents: () => ({
      ...props.components,
    }),
  });

  mdxComponent.value = mdxModule.default;
}

// 监听内容变化，重新编译
watch(() => props.content, compileMDX, { immediate: true });
</script>

<template>
  <!-- 正常渲染 -->
  <component :is="mdxComponent" />
</template>

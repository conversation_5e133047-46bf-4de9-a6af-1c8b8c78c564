/**
 * 将PCM数据转换为WAV格式的Blob
 * @param pcmData PCM数据数组
 * @param sampleRate 采样率，默认16000
 * @param channels 声道数，默认1
 * @returns WAV格式的Blob对象
 */
export function pcmToWavBlob(pcmData: number[], sampleRate = 16000, channels = 1): Blob {
  const { length } = pcmData;
  const arrayBuffer = new ArrayBuffer(44 + length * 2);
  const view = new DataView(arrayBuffer);

  // WAV文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i += 1) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  // RIFF chunk descriptor
  writeString(0, 'RIFF');
  view.setUint32(4, 36 + length * 2, true);
  writeString(8, 'WAVE');

  // FMT sub-chunk
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true); // Sub-chunk size
  view.setUint16(20, 1, true); // Audio format (PCM)
  view.setUint16(22, channels, true); // Number of channels
  view.setUint32(24, sampleRate, true); // Sample rate
  view.setUint32(28, sampleRate * channels * 2, true); // Byte rate
  view.setUint16(32, channels * 2, true); // Block align
  view.setUint16(34, 16, true); // Bits per sample

  // Data sub-chunk
  writeString(36, 'data');
  view.setUint32(40, length * 2, true);

  // PCM数据
  let offset = 44;
  for (let i = 0; i < length; i += 1) {
    view.setInt16(offset, pcmData[i], true);
    offset += 2;
  }

  return new Blob([arrayBuffer], { type: 'audio/wav' });
}

/**
 * 重采样PCM数据
 * @param pcmData 原始PCM数据
 * @param fromSampleRate 原始采样率
 * @param toSampleRate 目标采样率
 * @returns 重采样后的PCM数据
 */
export function resamplePcmData(pcmData: number[], fromSampleRate: number, toSampleRate: number): number[] {
  if (fromSampleRate === toSampleRate) {
    return pcmData.slice();
  }

  const ratio = fromSampleRate / toSampleRate;
  const outputLength = Math.floor(pcmData.length / ratio);
  const output = Array.from({ length: outputLength });

  for (let i = 0; i < outputLength; i += 1) {
    const sourceIndex = i * ratio;
    const index = Math.floor(sourceIndex);
    const fraction = sourceIndex - index;

    if (index + 1 < pcmData.length) {
      // 线性插值
      output[i] = Math.round(
        pcmData[index] * (1 - fraction) + pcmData[index + 1] * fraction,
      );
    } else {
      output[i] = pcmData[index];
    }
  }

  return output as number[];
}

/**
 * 将PCM数据转换为Base64格式（用于realtime API）
 * @param pcmData PCM数据数组（可能是16-bit整数或浮点数）
 * @param fromSampleRate 原始采样率
 * @param toSampleRate 目标采样率，默认24000（OpenAI Realtime API要求）
 * @returns Base64编码的音频数据
 */
export function pcmToBase64(
  pcmData: number[],
  fromSampleRate: number,
  toSampleRate: number = 24000,
): string {
  // 1. 优化：当采样率相同时跳过重采样，提高性能
  const resampledData = fromSampleRate === toSampleRate
    ? pcmData
    : resamplePcmData(pcmData, fromSampleRate, toSampleRate);

  // 2. 检测数据格式并转换为16-bit整数
  const int16Array = new Int16Array(resampledData.length);

  // 检测数据范围来判断格式
  const maxValue = Math.max(...resampledData.slice(0, 100));
  const minValue = Math.min(...resampledData.slice(0, 100));
  const isFloatFormat = maxValue <= 1.0 && minValue >= -1.0;

  for (let i = 0; i < resampledData.length; i += 1) {
    if (isFloatFormat) {
      // 浮点数格式：-1.0 到 1.0 转换为 -32768 到 32767
      const sample = Math.max(-1, Math.min(1, resampledData[i]));
      int16Array[i] = Math.round(sample * 32767);
    } else {
      // 整数格式：直接使用，但确保在有效范围内
      int16Array[i] = Math.max(-32768, Math.min(32767, Math.round(resampledData[i])));
    }
  }

  // 3. 转换为Uint8Array（字节数组）
  const uint8Array = new Uint8Array(int16Array.buffer);

  // 4. 转换为Base64 - 参考aisearch-openai-rag-audio的实现
  let binaryString = '';
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (let i = 0; i < uint8Array.length; i += 1) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  return btoa(binaryString);
}

/**
 * 将Base64音频数据转换为PCM数据（用于播放realtime API返回的音频）
 * @param base64Audio Base64编码的音频数据
 * @returns 16-bit PCM数据数组
 */
export function base64ToPcm(base64Audio: string): number[] {
  // 1. 解码Base64
  const binaryString = atob(base64Audio);

  // 2. 转换为Uint8Array
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i += 1) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }

  // 3. 转换为Int16Array
  const int16Array = new Int16Array(uint8Array.buffer);

  // 4. 转换为浮点数数组（Web Audio API 需要 -1 到 1 的范围）
  const floatArray: number[] = [];
  for (let i = 0; i < int16Array.length; i += 1) {
    // 将 16-bit 整数转换为 -1 到 1 的浮点数
    floatArray[i] = int16Array[i] / 32768.0;
  }

  return floatArray;
}

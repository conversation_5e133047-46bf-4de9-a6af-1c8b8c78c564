<script setup lang="ts">
defineProps<{
  sending?: boolean;
  block?: boolean;
}>();

const emits = defineEmits<{
  'on-send': [];
  'on-stop': [];
}>();
</script>


<template>
  <div
    class="bottom-action-area"
    :class="{ block }"
  >
    <ButtonAction
      v-if="!sending"
      color="#0086ff"
      :width="34"
      :height="34"
      :img-width="16"
      :img-height="16"
      @mousedown.prevent
      @click.stop="emits('on-send')"
    >
      <template #icon>
        <img src="@/assets/img/chat/icon-send.svg">
      </template>
    </ButtonAction>

    <ButtonAction
      v-else
      color="#0086ff"
      :width="34"
      :height="34"
      :img-width="18"
      :img-height="18"
      @mousedown.prevent
      @click.stop="emits('on-stop')"
    >
      <template #icon>
        <img src="@/assets/img/chat/icon-stop.svg">
      </template>
    </ButtonAction>
  </div>
</template>


<style scoped lang="less">
.bottom-action-area {
  display: flex;
  justify-content: flex-end;

  &.block {
    width: 100%;
  }
}
</style>

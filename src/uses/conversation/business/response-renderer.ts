/**
 * 响应渲染管理器
 * 负责管理AI响应的渲染效果，包括打字机效果、音频播放等
 */


import { useTypewriter } from '../../typewriter';
import { usePreCache } from '../../pre-cache';
import type { MessageManager } from './message-manager';
import type { InteractionManager } from '../interaction/base-interaction';

/**
 * 响应渲染管理器
 */
export class ResponseRenderer {
  /** 渲染配置 */
  private config: Conversation.ResponseRenderConfig;
  
  /** 消息管理器 */
  private messageManager: MessageManager;
  
  /** 交互管理器 */
  private interactionManager: InteractionManager;
  
  /** 打字机实例 */
  private typewriter: ReturnType<typeof useTypewriter>;
  
  /** 预缓存实例 */
  private preCache: ReturnType<typeof usePreCache>;
  
  /** 事件监听器映射 */
  private eventListeners: Map<string, Set<(...args: unknown[]) => void>> = new Map();

  constructor(
    messageManager: MessageManager,
    interactionManager: InteractionManager,
    config: Conversation.ResponseRenderConfig = {}
  ) {
    this.messageManager = messageManager;
    this.interactionManager = interactionManager;
    this.config = {
      enableTypewriter: true,
      typewriterSpeed: 50,
      autoPlayAudio: true,
      showLiveTranscript: true,
      ...config,
    };

    this.initializeComponents();
  }

  /**
   * 初始化组件
   */
  private initializeComponents(): void {
    // 初始化预缓存
    this.preCache = usePreCache();

    // 初始化打字机
    this.typewriter = useTypewriter({
      onStop: (messageId) => {
        this.handleTypewriterStop(messageId);
      },
      onComplete: (messageId) => {
        this.handleTypewriterComplete(messageId);
      },
      onContentUpdate: (messageId, content) => {
        this.handleTypewriterContentUpdate(messageId, content);
      },
    });
  }

  /**
   * 开始渲染AI响应
   */
  startRendering(message: Conversation.Message): void {
    if (this.config.enableTypewriter) {
      this.typewriter.start(message);
    }
    
    this.emit('renderingStarted', message.id);
  }

  /**
   * 停止渲染
   */
  stopRendering(messageId?: string): void {
    if (this.config.enableTypewriter) {
      this.typewriter.stop(messageId);
    }
    
    // 停止预缓存
    this.preCache.stopAll();
    
    this.emit('renderingStopped', messageId);
  }

  /**
   * 处理文本增量
   */
  handleTextDelta(messageId: string, textDelta: string): void {
    if (!this.config.enableTypewriter) {
      // 直接更新消息内容
      this.messageManager.appendMessageContent(messageId, textDelta);
      return;
    }

    // 使用预缓存处理文本增量
    const cache = this.preCache.getCache(messageId);
    cache.setExecFull(true);
    cache.addEvent(() => {
      this.typewriter.addContext(textDelta, messageId);
    });
  }

  /**
   * 处理音频增量
   */
  handleAudioDelta(messageId: string, audioData: string): void {
    if (!this.config.autoPlayAudio) {
      return;
    }

    // 使用预缓存处理音频增量
    const cache = this.preCache.getCache(messageId);
    cache.setExecFull(false);
    cache.addEvent(() => {
      this.playAudio(audioData, false);
    });
  }

  /**
   * 处理响应完成
   */
  handleResponseComplete(messageId: string): void {
    // 标记预缓存完成
    const cache = this.preCache.getCache(messageId);
    cache.markComplete(() => {
      if (this.config.enableTypewriter) {
        this.typewriter.completeContext(messageId);
      }
      
      // 播放最后的音频标记
      if (this.config.autoPlayAudio) {
        this.playAudio('', true);
      }
    });
  }

  /**
   * 播放音频
   */
  private playAudio(audioData: string, isLast: boolean): void {
    const voiceInteraction = this.interactionManager.getInteraction(Conversation.InteractionType.VOICE);
    if (voiceInteraction && 'playAudio' in voiceInteraction) {
      (voiceInteraction as any).playAudio(audioData, isLast);
    }
  }

  /**
   * 处理打字机停止
   */
  private handleTypewriterStop(messageId?: string): void {
    this.completeMessage(messageId);
    this.emit('typewriterStopped', messageId);
  }

  /**
   * 处理打字机完成
   */
  private handleTypewriterComplete(messageId?: string): void {
    this.completeMessage(messageId);
    this.emit('typewriterCompleted', messageId);
  }

  /**
   * 处理打字机内容更新
   */
  private handleTypewriterContentUpdate(messageId: string, content: string): void {
    // 直接设置消息内容（打字机效果）
    const message = this.messageManager.findMessageById(messageId);
    if (message) {
      message.content = content;
    }
    
    this.emit('contentUpdated', messageId, content);
  }

  /**
   * 完成消息渲染
   */
  private completeMessage(messageId?: string): void {
    if (!messageId) {
      return;
    }

    // 检查是否需要停止AI回复状态
    const shouldStopAiReply = this.shouldStopAiReplyState();
    
    // 清理当前AI回复消息
    this.messageManager.clearCurrentAiMessage();
    this.messageManager.updateMessageStatus(messageId, MessageStatus.COMPLETE);
    
    this.emit('messageCompleted', messageId, shouldStopAiReply);
  }

  /**
   * 判断是否应该停止AI回复状态
   */
  private shouldStopAiReplyState(): boolean {
    // 如果是文本模式，或者静音状态，或者音频没有在播放，则停止AI回复状态
    const voiceInteraction = this.interactionManager.getInteraction(Conversation.InteractionType.VOICE);
    const isVoicePlaying = voiceInteraction && 'getAudioControl' in voiceInteraction 
      ? (voiceInteraction as any).getAudioControl().isPlaying()
      : false;
    
    const isMuted = voiceInteraction && 'isMuted' in voiceInteraction
      ? (voiceInteraction as any).isMuted.value
      : false;

    return isMuted || !isVoicePlaying;
  }

  /**
   * 更新渲染配置
   */
  updateConfig(config: Partial<Conversation.ResponseRenderConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): Conversation.ResponseRenderConfig {
    return { ...this.config };
  }

  /**
   * 检查打字机是否正在运行
   */
  isTypewriterActive(): boolean {
    return this.typewriter.getIsTyping();
  }

  /**
   * 检查指定消息的上下文是否完成
   */
  isContextComplete(messageId: string): boolean {
    return this.typewriter.getIsContextComplete(messageId);
  }

  /**
   * 注册事件监听器
   */
  on(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: (...args: unknown[]) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: unknown[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`[ResponseRenderer] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopRendering();
    this.eventListeners.clear();
  }
}

/**
 * 创建响应渲染管理器
 */
export function createResponseRenderer(
  messageManager: MessageManager,
  interactionManager: InteractionManager,
  config?: Conversation.ResponseRenderConfig
): ResponseRenderer {
  return new ResponseRenderer(messageManager, interactionManager, config);
}

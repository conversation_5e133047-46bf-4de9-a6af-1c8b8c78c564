import { type App } from 'vue';
import { AccessTokenInvalidError } from '@/errors/access-token-invalid';
import { RefreshTokenInvalidError } from '@/errors/refresh-token-invalid';
import { AccountDisabledError } from '@/errors/account-disabled';
import emitter from '@/utils/event-bus';


export const ErrorHandlerPlugin = {
  install(app: App) {
    function errorHandler(error: Error) {
      if (error instanceof AccessTokenInvalidError) {
        emitter.emit('accessTokenInvalidError', error);
        return;
      }

      if (error instanceof RefreshTokenInvalidError) {
        emitter.emit('refreshTokenInvalidError', error);
        return;
      }

      if (error instanceof AccountDisabledError) {
        emitter.emit('accountDisabledError', error);
      }
    }

    Object.assign(app.config, {
      errorHandler,
    });
  },
};

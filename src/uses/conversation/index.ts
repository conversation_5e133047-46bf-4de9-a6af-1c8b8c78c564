/**
 * 重构后的对话系统主入口
 * 作为各层的协调器，提供统一的对话管理接口
 */

import { ref, computed, onUnmounted } from 'vue';
import { nanoid } from 'nanoid';
import URI from 'urijs';

import { NativeEvent } from '@/config/native-event';
import { ChatMode } from '@/consts/chat-mode';
import { AppSource } from '@/consts/app-source';
import { CustomEventName } from '@/consts/custom-event-name';
import { DeviceType } from '@/consts/device-type';
import { isIOS } from '@/utils/is';
import { convertPCLocale } from '@/helps/locale';

// 连接层
import { connectionManager } from './connection/base-connection';
import { OpenAIRealtimeConnectionFactory } from './connection/openai-realtime-connection';

// 交互层
import { InteractionManager } from './interaction/base-interaction';
import { VoiceInteraction } from './interaction/voice-interaction';
import { TextInteraction } from './interaction/text-interaction';

// 业务层
import { createMessageManager } from './business/message-manager';
import { ConversationFlow } from './business/conversation-flow';
import { createResponseRenderer } from './business/response-renderer';

// 历史消息和其他功能
import { useMessageHistory } from '../message-history';

/**
 * 获取接口需要参数
 */
function getCustomPayloadMessage() {
  return {
    type: 'custom.payload',
    sessionId: nanoid(),
    payload: {
      authorization: native.user.getAccessToken(),
      lang: convertPCLocale(native.i18n.language),
      userId: native.user.getUserId(),
      source: DeviceType.APP,
      appSource: isIOS() ? AppSource.IOS : AppSource.ANDROID,
    },
  };
}

/**
 * 获取realtime地址
 */
function getRealtimeEndpoint() {
  const uri = new URI(process.env.AI_MARY_API_BASE_URL);
  uri.segment('realtime');
  return uri.href();
}

/**
 * 打印日志
 */
function log(str: string) {
  console.log(str);
  native.logger.log(str);
}

/**
 * 重构后的useChat hook
 * 作为各层的协调器，提供统一的对话管理接口
 */
export function useChat() {
  // ==================== 核心组件初始化 ====================
  
  // 注册连接工厂
  connectionManager.registerFactory('openai-realtime', new OpenAIRealtimeConnectionFactory());
  
  // 创建连接
  const connection = connectionManager.createConnection('openai-realtime', {
    endpoint: getRealtimeEndpoint(),
    autoConnect: true,
    maxRetries: 5,
    reconnectDelay: 3000,
  });

  // 创建消息管理器
  const messageManager = createMessageManager();

  // 创建交互管理器
  const interactionManager = new InteractionManager();

  // 创建语音交互
  const voiceInteraction = new VoiceInteraction(connection);
  interactionManager.registerInteraction(voiceInteraction);

  // 创建文本交互
  const textInteraction = new TextInteraction(connection);
  interactionManager.registerInteraction(textInteraction);

  // 创建对话流程控制器
  const conversationFlow = new ConversationFlow(messageManager, connection, interactionManager);

  // 创建响应渲染管理器
  const responseRenderer = createResponseRenderer(messageManager, interactionManager);

  // 历史消息管理
  const messageHistory = useMessageHistory({
    insertData: (data) => {
      messageManager.insertHistoryMessages(data);
    },
  });

  // ==================== 状态管理 ====================
  
  // 标识不在当前页面的状态
  const isNotCurrentPage = ref(false);

  // 计算属性：从各个组件获取状态
  const currentMode = computed(() => conversationFlow.conversationState.value.mode);
  const isConnecting = computed(() => conversationFlow.conversationState.value.isConnecting);
  const isConnectError = computed(() => conversationFlow.conversationState.value.hasConnectionError);
  const isRecording = computed(() => voiceInteraction.isRecording.value);
  const volume = computed(() => voiceInteraction.volume.value);
  const isAiReplying = computed(() => conversationFlow.conversationState.value.isAiReplying);
  const inputValue = computed({
    get: () => textInteraction.inputText.value,
    set: (value: string) => textInteraction.updateInputText(value),
  });
  const isMuted = computed(() => voiceInteraction.isMuted.value);

  // ==================== 初始化和事件设置 ====================

  // 初始化所有组件
  async function initialize() {
    try {
      await voiceInteraction.initialize();
      await textInteraction.initialize();
      await conversationFlow.initialize();
      
      // 设置连接事件监听器
      setupConnectionEvents();
      
      // 设置交互事件监听器
      setupInteractionEvents();
      
      // 设置业务事件监听器
      setupBusinessEvents();
      
      // 启动连接
      await connection.connect();
      
      log('对话系统初始化完成');
    } catch (error) {
      log(`对话系统初始化失败: ${error}`);
      throw error;
    }
  }

  /**
   * 设置连接事件监听器
   */
  function setupConnectionEvents() {
    connection.on('connected', () => {
      log('ws连接成功');
      // 发送后端需要参数
      connection.sendMessage(getCustomPayloadMessage());
      
      setTimeout(() => {
        if (currentMode.value === ChatMode.SPEECH) {
          conversationFlow.startConversation(ChatMode.SPEECH);
        }
      }, 2000);
    });

    connection.on('disconnected', () => {
      log('ws连接关闭');
    });

    connection.on('error', (error: Error) => {
      log(`ws连接错误: ${error.message}`);
    });

    // 自定义事件处理
    connection.on('customEvent', (eventName: string, data: any) => {
      log(`自定义事件: ${eventName}`);
      handleCustomEvent(eventName, data);
    });
  }

  /**
   * 设置交互事件监听器
   */
  function setupInteractionEvents() {
    // 语音交互事件
    voiceInteraction.on('userSpeechStart', () => {
      log('用户开始说话');
      if (isAiReplying.value) {
        conversationFlow.interruptAiReply();
      }
    });

    voiceInteraction.on('userTranscriptionComplete', (transcript: string, messageId: string) => {
      log(`用户语音转录完成: ${transcript}`);
      conversationFlow.handleUserInputComplete(transcript, messageId);
    });

    voiceInteraction.on('aiAudioDelta', (audioData: string, messageId: string) => {
      log('AI语音增量');
      if (shouldPlayAudio()) {
        responseRenderer.handleAudioDelta(messageId, audioData);
      }
    });

    voiceInteraction.on('aiTranscriptDelta', (transcript: string, messageId: string) => {
      log(`AI文本增量-${messageId}: ${transcript}`);
      responseRenderer.handleTextDelta(messageId, transcript);
    });

    // 文本交互事件
    textInteraction.on('messageSent', (text: string) => {
      log(`文本消息发送: ${text}`);
      // 添加用户消息到界面
      messageManager.addUserMessage(text);
    });
  }

  /**
   * 设置业务事件监听器
   */
  function setupBusinessEvents() {
    // 对话流程事件
    conversationFlow.on('aiReplyStarted', (messageId: string) => {
      log(`AI开始回复: ${messageId}`);
      const message = messageManager.findMessageById(messageId);
      if (message) {
        responseRenderer.startRendering(message);
      }
    });

    conversationFlow.on('aiReplyCompleted', (messageId: string) => {
      log(`AI回复完成: ${messageId}`);
      responseRenderer.handleResponseComplete(messageId);
    });

    conversationFlow.on('toolCallStarted', (_toolCallId: string) => {
      log('工具调用开始');
    });

    // 响应渲染事件
    responseRenderer.on('messageCompleted', (messageId: string, shouldStopAiReply: boolean) => {
      if (shouldStopAiReply) {
        conversationFlow.updateConversationState({ isAiReplying: false });
      }
    });
  }

  // ==================== 用户操作方法 ====================

  /**
   * 切换录音状态
   */
  async function onToggleRecording() {
    log(`切换录音状态: ${!isRecording.value}`);
    await voiceInteraction.toggleRecording();
  }

  /**
   * 切换静音状态
   */
  async function onToggleMuted() {
    log(`切换静音状态: ${!isMuted.value}`);
    await voiceInteraction.toggleMuted();
  }

  /**
   * 切换对话模式
   */
  async function onSwitchMode(mode: ChatMode) {
    log(`切换对话模式: ${mode}`);
    await conversationFlow.switchMode(mode);
  }

  /**
   * 发送文本消息
   */
  async function onSendMessage() {
    const text = inputValue.value.trim();
    if (!text) {
      return;
    }

    try {
      await textInteraction.sendMessage(text);
    } catch (error) {
      log(`发送消息失败: ${error}`);
      throw error;
    }
  }

  /**
   * 打断AI回复
   */
  async function onInterruptAiReply() {
    log('打断AI回复');
    await conversationFlow.interruptAiReply();
  }

  // ==================== 辅助方法 ====================

  /**
   * 判断是否需要播放音频
   */
  function shouldPlayAudio(): boolean {
    return (
      !isMuted.value &&
      isAiReplying.value &&
      !isNotCurrentPage.value &&
      currentMode.value === ChatMode.SPEECH
    );
  }

  /**
   * 处理自定义事件
   */
  function handleCustomEvent(eventName: string, data: any) {
    switch (eventName) {
      case CustomEventName.OPEN_APP:
        log(`打开应用: ${data.serviceCode}`);
        voiceInteraction.stop();
        isNotCurrentPage.value = true;
        native.navigator.v2.launchService({
          serviceCode: data.serviceCode,
        });
        break;
      default:
        break;
    }
  }

  // ==================== 生命周期管理 ====================

  // 页面状态监听
  native.navigator.addEventListener(NativeEvent.WEBVIEW_RESUME, () => {
    isNotCurrentPage.value = false;

    const currentMessage = messageManager.getCurrentAiMessage();
    if (currentMessage && responseRenderer.isContextComplete(currentMessage.id as string)) {
      responseRenderer.stopRendering(currentMessage.id as string);
    }

    if (isRecording.value && currentMode.value === ChatMode.SPEECH) {
      voiceInteraction.start();
    }

    if (isAiReplying.value) {
      conversationFlow.updateConversationState({ isAiReplying: false });
    }
  });

  // 组件卸载时清理资源
  onUnmounted(async () => {
    await conversationFlow.cleanup();
    await interactionManager.cleanup();
    await connectionManager.cleanup();
    responseRenderer.cleanup();
  });

  // 初始化系统
  initialize();

  // 加载历史消息
  messageHistory.load();

  // ==================== 返回接口 ====================

  return {
    // 状态
    currentMode,
    isConnecting,
    isConnectError,
    isRecording,
    isAiReplying,
    messages: messageManager.messages,
    volume,
    inputValue,
    isMuted,
    historyLoading: messageHistory.loading,
    historyInitLoading: messageHistory.initLoading,

    // 操作方法
    onInterruptAiReply,
    onSwitchMode,
    onSendMessage,
    onToggleRecording,
    onToggleMuted,
    loadMessagesHistory: messageHistory.load,
  };
}

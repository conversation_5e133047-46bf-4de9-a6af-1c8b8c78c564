import { onMounted, onUnmounted, ref } from 'vue';

export function useKeyboard() {
  const isKeyboardVisible = ref(false);
  const keyboardHeight = ref(0);
  let initialViewportHeight = 0;
  let resizeTimer: number | null = null;

  // 检测键盘弹起/收起
  function detectKeyboard() {
    const currentHeight = window.visualViewport?.height ?? window.innerHeight;
    const heightDifference = initialViewportHeight - currentHeight;

    // 如果高度差超过100px，认为是键盘弹出
    const threshold = 100;

    isKeyboardVisible.value = heightDifference > threshold;
    keyboardHeight.value = heightDifference > threshold ? heightDifference : 0;
  }

  // 防抖处理resize事件
  function handleResize() {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }

    resizeTimer = window.setTimeout(() => {
      detectKeyboard();
    }, 100);
  }

  // Visual Viewport API 支持检测
  function handleVisualViewportChange() {
    detectKeyboard();
  }

  onMounted(() => {
    // 记录初始视口高度
    initialViewportHeight = window.visualViewport?.height ?? window.innerHeight;

    // 优先使用 Visual Viewport API
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange);
    } else {
      // 降级到 window resize 事件
      window.addEventListener('resize', handleResize);
    }
  });

  onUnmounted(() => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }

    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
    } else {
      window.removeEventListener('resize', handleResize);
    }
  });

  return {
    isKeyboardVisible,
    keyboardHeight,
  };
}

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import jsonBigInt from 'json-bigint';

import { nativeAdapter } from './native-adapter';
import { interceptorAjax } from './interceptors/ajax';
import { interceptorSignature } from './interceptors/signature';
import { interceptorNilFilter } from './interceptors/nil-filter';
import { interceptorNonce } from './interceptors/nonce';
import { interceptorLocale } from './interceptors/locale';
import { interceptorError } from './interceptors/error';


export type CreateRequestOptions = AxiosRequestConfig & {
  salt?: string;
};

const DEFAULT_TIMEOUT = 20 * 1000; // 默认超时时间为20s

export function createRequest({ baseURL, timeout, responseType, headers, salt }: CreateRequestOptions): AxiosInstance {
  const xhr = axios.create({
    adapter: nativeAdapter,
    baseURL,
    timeout: (Number.isFinite(timeout) ? timeout : DEFAULT_TIMEOUT),
    responseType,
    headers: {
      ...headers,
    },
    transformResponse: [
      (data) => {
        try {
          return jsonBigInt({ storeAsString: true }).parse(data);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.warn('Failed to parse response data', data);
          return data;
        }
      },
    ],
  });

  xhr.interceptors.request.use(interceptorAjax);
  xhr.interceptors.request.use(interceptorSignature({ salt }));
  xhr.interceptors.request.use(interceptorNilFilter);
  xhr.interceptors.request.use(interceptorNonce);
  xhr.interceptors.request.use(interceptorLocale);

  xhr.interceptors.response.use(interceptorError);

  return xhr;
}

<script setup lang="ts" name="WmLoading">
import { computed } from 'vue';
import WmLoadingIcon from './wm-loading-icon.vue';

const props = withDefaults(defineProps<{
  border?: boolean
}>(), {
  border: true,
});

const classes = computed(() => ({
  border: props.border,
}));
</script>


<template>
  <div
    class="wm-loading"
    :class="classes"
  >
    <div class="wm-loading-icon">
      <WmLoadingIcon />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wm-loading {
  display: inline-block;
  box-sizing: border-box;
  width: 66px;
  height: 66px;
  padding: 22px 0;
  overflow: hidden;
}

.wm-loading.border {
  background-color: #fff;
  border: 1px solid #e3e3e3;
  border-radius: 5px;
}

.wm-loading-icon {
  display: block;
  width: 36px;
  height: 22px;
  margin: 0 auto;
  font-size: 22px;
  animation: 0.5s cubic-bezier(0.65, 0.05, 0.36, 1) infinite alternate spin;
}

.wm-loading-text {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(45deg);
  }
}
</style>

{"compilerOptions": {"target": "ES5", "module": "CommonJS", "allowJs": false, "alwaysStrict": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "baseUrl": ".", "paths": {"tslib": ["node_modules/tslib/tslib.d.ts"], "@/*": ["src/*"]}, "lib": ["ES2015", "esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["vue-i18n", "src", "types/*.d.ts"], "exclude": ["node_modules"]}
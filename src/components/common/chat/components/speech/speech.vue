<script setup lang="ts">
import ButtonAction from '../button-action';
import VoiceStatus from './voice-status.vue';

import IconVoice from '@/assets/img/chat/icon-voice.svg';
import IconVoiceDisabled from '@/assets/img/chat/icon-voice-disabled.svg';
import { ChatMode } from '@/consts/chat-mode';

// Props - 接收外部状态
interface SpeechProps {
  isConnecting: boolean;
  isConnectError: boolean;
  isRecording: boolean;
  isAiReplying: boolean;
  volume: number;
}

defineProps<SpeechProps>();

const emits = defineEmits<{
  'on-switch-mode': [mode: ChatMode];
  'on-toggle-recording': [];
  'on-interrupt-ai-replying': [];
}>();

function onSwitchMode(mode: ChatMode) {
  emits('on-switch-mode', mode);
}

function onToggleRecording() {
  emits('on-toggle-recording');
}

function onInterruptAiReply() {
  emits('on-interrupt-ai-replying');
}

</script>


<template>
  <div class="chat-speech">
    <div class="chat-speech-inner">
      <!-- 是否靜音 -->
      <ButtonAction
        :img-width="34"
        :img-height="34"
        @click="onToggleRecording()"
      >
        <template #icon>
          <img :src="isRecording ? IconVoice : IconVoiceDisabled">
        </template>
      </ButtonAction>

      <!-- 語音說話狀態 -->
      <VoiceStatus
        class="voice-status"
        :is-recording="isRecording"
        :is-ai-replying="isAiReplying"
        :is-connecting="isConnecting"
        :is-connect-error="isConnectError"
        :volume="volume"
        @on-interrupt-ai-replying="onInterruptAiReply()"
      />

      <!-- 关闭 -->
      <ButtonAction
        :img-width="32"
        :img-height="32"
        @click="onSwitchMode(ChatMode.INPUT)"
      >
        <template #icon>
          <img src="@/assets/img/chat/icon-close.svg">
        </template>
      </ButtonAction>
    </div>
  </div>
</template>


<style scoped lang="less">
.chat-speech {
  padding: 15px 28px;

  .chat-speech-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .voice-status {
      flex: 1;
    }
  }
}
</style>

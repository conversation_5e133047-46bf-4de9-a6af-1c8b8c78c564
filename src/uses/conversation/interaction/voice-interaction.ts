/**
 * 语音交互实现
 * 处理语音输入、录音控制、音频播放等语音相关功能
 */

import { ref, type Ref } from 'vue';
import { BaseInteraction, InteractionState } from './base-interaction';
import type { BaseConnection } from '../connection/base-connection';
import { useAudioRecorder } from '../../audio-recorder';
import { useAudioPlayer } from '../../audio-player';

/**
 * 语音交互实现类
 */
export class VoiceInteraction extends BaseInteraction {
  /** 是否正在录音 */
  public readonly isRecording: Ref<boolean> = ref(false);
  
  /** 当前音量 */
  public readonly volume: Ref<number> = ref(0);
  
  /** 是否静音 */
  public readonly isMuted: Ref<boolean> = ref(false);
  
  /** 录音控制器 */
  private audioRecorder: ReturnType<typeof useAudioRecorder> | null = null;
  
  /** 音频播放器 */
  private audioPlayer: ReturnType<typeof useAudioPlayer> | null = null;

  constructor(connection: BaseConnection) {
    super(connection, Conversation.InteractionType.VOICE);
  }

  /**
   * 初始化语音交互
   */
  async initialize(): Promise<void> {
    try {
      // 初始化音频播放器
      this.audioPlayer = useAudioPlayer({
        onCompleted: () => {
          this.emit('audioPlaybackComplete');
        },
      });

      // 初始化录音器
      this.audioRecorder = useAudioRecorder({
        onAudioRecorded: (audioData: string, volumeValue: number) => {
          this.handleAudioRecorded(audioData, volumeValue);
        },
      });

      // 设置连接事件监听器
      this.setupVoiceConnectionListeners();

      this.setState(InteractionState.IDLE);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 启动语音交互
   */
  async start(): Promise<void> {
    if (!this.audioRecorder) {
      throw new Error('Voice interaction not initialized');
    }

    try {
      this.setState(InteractionState.ACTIVE);
      
      // 启动录音
      this.audioRecorder.start();
      this.isRecording.value = true;
      
      this.emit('voiceInteractionStarted');
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 停止语音交互
   */
  async stop(): Promise<void> {
    try {
      // 停止录音
      if (this.audioRecorder && this.isRecording.value) {
        this.audioRecorder.stop();
        this.isRecording.value = false;
      }

      // 停止音频播放
      if (this.audioPlayer) {
        this.audioPlayer.stop();
      }

      this.setState(InteractionState.IDLE);
      this.emit('voiceInteractionStopped');
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 重置语音交互状态
   */
  reset(): void {
    this.volume.value = 0;
    this.isRecording.value = false;
    
    if (this.audioPlayer) {
      this.audioPlayer.reset();
    }
    
    this.setState(InteractionState.IDLE);
  }

  /**
   * 切换录音状态
   */
  async toggleRecording(): Promise<void> {
    if (this.isRecording.value) {
      await this.pauseRecording();
    } else {
      await this.resumeRecording();
    }
  }

  /**
   * 暂停录音
   */
  async pauseRecording(): Promise<void> {
    if (this.audioRecorder && this.isRecording.value) {
      // 注意：这里不实际停止录音器，只是标记状态
      // 因为原生问题（语音播放时关闭录音会导致播放音量变小）
      this.isRecording.value = false;
      this.volume.value = 0;
      this.emit('recordingPaused');
    }
  }

  /**
   * 恢复录音
   */
  async resumeRecording(): Promise<void> {
    if (this.audioRecorder && !this.isRecording.value) {
      this.isRecording.value = true;
      this.emit('recordingResumed');
    }
  }

  /**
   * 切换静音状态
   */
  async toggleMuted(): Promise<void> {
    this.isMuted.value = !this.isMuted.value;
    
    if (this.isMuted.value) {
      // 静音时停止音频播放
      if (this.audioPlayer) {
        this.audioPlayer.stop();
      }
    } else {
      // 取消静音时重置播放器
      if (this.audioPlayer) {
        this.audioPlayer.reset();
      }
    }
    
    this.emit('mutedStateChanged', this.isMuted.value);
  }

  /**
   * 播放音频
   */
  async playAudio(audioData: string, isLast = false): Promise<void> {
    if (!this.audioPlayer || this.isMuted.value) {
      return;
    }

    try {
      this.audioPlayer.play(audioData, isLast);
      this.emit('audioPlaybackStarted', audioData, isLast);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 停止音频播放
   */
  async stopAudioPlayback(): Promise<void> {
    if (this.audioPlayer) {
      this.audioPlayer.stop();
      this.emit('audioPlaybackStopped');
    }
  }

  /**
   * 清空音频缓冲区
   */
  async clearAudioBuffer(): Promise<void> {
    if (this.connection && 'clearAudioBuffer' in this.connection) {
      await (this.connection as any).clearAudioBuffer();
      this.emit('audioBufferCleared');
    }
  }

  /**
   * 获取录音状态
   */
  getRecordingState(): Conversation.RecordingControl {
    return {
      start: () => this.resumeRecording(),
      stop: () => this.pauseRecording(),
      isRecording: () => this.isRecording.value,
      getVolume: () => this.volume.value,
    };
  }

  /**
   * 获取音频控制接口
   */
  getAudioControl(): Conversation.AudioControl {
    return {
      play: (audioData: string, isLast?: boolean) => this.playAudio(audioData, isLast),
      stop: () => this.stopAudioPlayback(),
      reset: () => this.audioPlayer?.reset(),
      isPlaying: () => this.audioPlayer?.getIsPlaying() || false,
    };
  }

  /**
   * 处理录音数据
   */
  private handleAudioRecorded(audioData: string, volumeValue: number): void {
    if (this.isRecording.value && this.isActive()) {
      this.volume.value = volumeValue;
      
      // 发送音频数据到连接
      if (this.connection && 'addUserAudio' in this.connection) {
        (this.connection as any).addUserAudio(audioData);
      }
      
      this.emit('audioRecorded', audioData, volumeValue);
    } else {
      this.volume.value = 0;
    }
  }

  /**
   * 设置语音连接事件监听器
   */
  private setupVoiceConnectionListeners(): void {
    // 用户开始说话
    this.connection.on('userSpeechStart', () => {
      this.emit('userSpeechStart');
    });

    // 用户停止说话
    this.connection.on('userSpeechEnd', (data: unknown) => {
      this.emit('userSpeechEnd', data);
    });

    // 用户语音转录完成
    this.connection.on('userTranscriptionComplete', (transcript: string, messageId: string) => {
      this.emit('userTranscriptionComplete', transcript, messageId);
    });

    // AI音频增量
    this.connection.on('aiAudioDelta', (audioData: string, messageId: string) => {
      this.emit('aiAudioDelta', audioData, messageId);
    });

    // AI语音转录增量
    this.connection.on('aiTranscriptDelta', (transcript: string, messageId: string) => {
      this.emit('aiTranscriptDelta', transcript, messageId);
    });
  }

  /**
   * 连接建立时的处理
   */
  protected onConnectionEstablished(): void {
    // 连接建立后，如果处于活跃状态，启动录音
    if (this.isActive() && this.audioRecorder) {
      this.audioRecorder.start();
    }
  }

  /**
   * 连接丢失时的处理
   */
  protected onConnectionLost(): void {
    super.onConnectionLost();
    
    // 停止所有音频相关操作
    if (this.audioPlayer) {
      this.audioPlayer.stop();
    }
    
    this.volume.value = 0;
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    super.cleanup();
    
    this.isRecording.value = false;
    this.volume.value = 0;
    this.isMuted.value = false;
    
    // 清理音频资源
    this.audioRecorder = null;
    this.audioPlayer = null;
  }
}

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  isRecording: boolean;
  isAiReplying: boolean;
  isConnecting: boolean;
  isConnectError: boolean;
  volume: number;
}>();

const { t } = useI18n();

const emits = defineEmits<{
  'on-interrupt-ai-replying': [];
}>();

function onInterruptAiReply() {
  emits('on-interrupt-ai-replying');
}

const statusText = computed(() => {
  switch (true) {
    case props.isConnecting:
      return t('chat.footer.speech.connecting');
    case props.isConnectError:
      return t('chat.footer.speech.connectingError');
    case !props.isRecording:
      return t('chat.footer.speech.muted');
    case props.isAiReplying:
      return t('chat.footer.speech.speaking');
    default:
      return t('chat.footer.speech.listening');
  }
});

</script>


<template>
  <div class="voice-status">
    <div
      v-if="isConnecting || isConnectError || !isRecording"
      class=" voice-status-tip"
    >
      {{ statusText }}
    </div>

    <template v-else>
      <!-- AI說話 -->
      <div
        v-if="isAiReplying"
        class="ai-speaking"
        @click="onInterruptAiReply()"
      >
        <div class="block" />
        <div class="voice-status-tip">
          {{ statusText }}
        </div>
      </div>

      <!-- 用户說話 -->
      <div
        v-else
        class="user-speaking"
      >
        <VoiceWaveform
          class="voice-waveform"
          :volume="volume"
        />

        <div class="voice-status-tip">
          {{ statusText }}
        </div>
      </div>
    </template>
  </div>
</template>


<style scoped lang="less">
.voice-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 66px;
  height: 66px;

  .voice-status-tip {
    color: #777;
    text-align: center;
  }

  .user-speaking, .ai-speaking {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;

    .block {
      width: 20px;
      height: 20px;
      margin-top: 12px;
      background-color: #999;
      border-radius: 3px;
    }

    .voice-status-tip {
      margin: 6px 0;
    }
  }

  .user-speaking {
    justify-content: flex-end;

    .voice-waveform {
      flex: 1;
    }
  }
}
</style>

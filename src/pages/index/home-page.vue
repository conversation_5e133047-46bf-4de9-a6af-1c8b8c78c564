<script setup lang="ts">
import Chat from '@/components/common/chat';
import TitleBar from './components/title-bar.vue';

import BgImg from '@/assets/img/bg.png';
import { ChatMode } from '@/consts/chat-mode';
import { useChat } from '@/uses/chat';

const chat = useChat();
</script>


<template>
  <div
    class="page-container"
    :style="{
      background: `url(${BgImg}) no-repeat 100% 100% / cover`,
    }"
  >
    <div class="title">
      <TitleBar
        :is-muted="chat.isMuted.value"
        :is-show-muted="chat.currentMode.value === ChatMode.SPEECH"
        @on-toggle-muted="chat.onToggleMuted"
      />
    </div>

    <div class="body">
      <Chat
        v-model:input-value="chat.inputValue.value"
        v-model:history-loading="chat.historyLoading.value"
        :content-loading="chat.historyInitLoading.value"
        :messages="chat.messages.value"
        :current-mode="chat.currentMode.value"
        :is-ai-replying="chat.isAiReplying.value"
        :is-connecting="chat.isConnecting.value"
        :is-connect-error="chat.isConnectError.value"
        :is-recording="chat.isRecording.value"
        :is-user-speaking="chat.isAiReplying.value"
        :volume="chat.volume.value"
        @on-send-message="chat.onSendMessage"
        @on-toggle-recording="chat.onToggleRecording"
        @on-switch-mode="chat.onSwitchMode"
        @on-interrupt-ai-replying="chat.onInterruptAiReply"
        @on-load-history="chat.loadMessagesHistory"
      />
    </div>
  </div>
</template>


<style scoped lang="less">
.page-container {
  .title {
    height: 34px;
    padding: 9px 16px;
  }

  .body {
    height: calc(100% - 32px - 18px);
  }
}
</style>

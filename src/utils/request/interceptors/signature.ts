import type { InternalAxiosRequestConfig } from 'axios';
import _ from 'lodash';
import { md5 } from 'js-md5';


function isFormData(thing: unknown) {
  return thing instanceof FormData;
}

function pairsQuery(query: Record<string, unknown>): unknown[][] {
  const ps = _.toPairs(query).map(([k, v]) => {
    if (_.isArray(v)) {
      return [k, (_.isEmpty(v) ? null : v[0])];
    }

    return [k, v];
  });

  return ps;
}

// 处理Body为正常的JSON键值对
function pairsBodyGeneral(body: Record<string, unknown> | FormData | unknown[]): unknown[][] {
  if (isFormData(body)) {
    return [];
  }

  if (_.isArray(body)) {
    return [];
  }

  const ps = _.toPairs(body).map(([k, v]) => {
    if (_.isObject(v)) {
      return [k, JSON.stringify(v)];
    }

    return [k, v];
  });

  return ps;
}

// 处理Body为JSON数组格式
function pairsBodyArray(body: Record<string, unknown>[]) {
  if (isFormData(body)) {
    return [];
  }

  if (!_.isArray(body)) {
    return [];
  }

  let pairs: unknown[][] = [];
  _.forEach(body, (it) => {
    const c = _.map(_.keys(it).sort(), (k) => {
      const v = it[k];
      if (_.isObject(v)) {
        return [k, JSON.stringify(v)];
      }

      return [k, v];
    });

    pairs = pairs.concat(c);
  });

  return pairs;
}

function signatureString(pairs: unknown[][], sort = true) {
  const process = _.flow(
    (pairs_) => {
      return _.filter(pairs_, ([, v]) => {
        return !_.isNil(v);
      });
    },

    (pairs_) => {
      if (sort) {
        return _.sortBy(pairs_, ([k]) => k);
      }

      return pairs;
    },

    (pairs_) => {
      return pairs_.map(([k, v]) => `${k}=${v}`).join('');
    },
  );

  return process(pairs);
}

function signature(salt: string, query: Record<string, unknown>, body: Record<string, unknown>[]) {
  const encrypted = (salt_: string, raw: string) => {
    return md5(`${raw}${salt_}`);
  };

  const pq = pairsQuery(query);
  const pbg = pairsBodyGeneral(body);
  const pba = pairsBodyArray(body);
  const s1 = signatureString(pq.concat(pbg));
  const s2 = signatureString(pba, false);
  const es = encrypted(salt, `${s1}${s2}`);
  return es;
}

interface SignatureInterceptorOptions {
  salt?: string;
}

export function interceptorSignature({ salt = '' }: SignatureInterceptorOptions)
  : (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig {
  return (config: InternalAxiosRequestConfig) => {
    const s = signature(
      salt,
      config.params,
      config.data,
    );

    Object.assign(config.params, {
      signature: s,
    });

    return config;
  };
}

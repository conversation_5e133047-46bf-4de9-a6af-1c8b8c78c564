export function useRecorder({ onDataAvailable }: {
  onDataAvailable: (data: Iterable<number>) => void
}) {
  let audioContext: AudioContext | null = null;
  let mediaStream: MediaStream | null = null;
  let mediaStreamSource: MediaStreamAudioSourceNode | null = null;
  let workletNode: AudioWorkletNode | null = null;

  async function stop() {
    if (mediaStream) {
      mediaStream.getTracks().forEach((track) => track.stop());
      mediaStream = null;
    }

    if (audioContext) {
      audioContext.close();
      audioContext = null;
    }

    mediaStreamSource = null;
    workletNode = null;
  }

  async function start(stream: MediaStream) {
    try {
      // 如果已经有audioContext，先关闭它（参考demo项目）
      if (audioContext) {
        await audioContext.close();
      }

      audioContext = new AudioContext({ sampleRate: 24000 });
      // 根据环境确定 worklet 文件路径
      const workletPath = process.env.NODE_ENV === 'production'
        ? './static/audio-processor-worklet.js'
        : 'audio-processor-worklet.js';
      await audioContext.audioWorklet.addModule(workletPath);

      mediaStream = stream;
      mediaStreamSource = audioContext.createMediaStreamSource(mediaStream);

      workletNode = new AudioWorkletNode(audioContext, 'audio-processor-worklet');
      workletNode.port.onmessage = (event) => {
        onDataAvailable(event.data.buffer);
      };

      mediaStreamSource.connect(workletNode);
      workletNode.connect(audioContext.destination);
    } catch (error) {
      console.error('录制器启动失败:', error);
      await stop();
    }
  }

  return {
    stop,
    start,
  };
}

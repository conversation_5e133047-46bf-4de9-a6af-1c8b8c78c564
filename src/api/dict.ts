
import _ from 'lodash';
import { valueByLocale } from '@/helps/locale';
import { CommonModelApi } from '@/api/common/common-model-api';


export class DictApi extends CommonModelApi<ICommonDictItem[]> {
  url() {
    return '/datas/tags';
  }

  async sendWithSpecifyType(): Promise<ICommonDictItem[]> {
    const data = await super.send();
    const dataTagVos = _.get(data, 'model', []) as ICommonDictItem[];

    return dataTagVos.map((item) => {
      const { code, name, enName } = item;
      const label = valueByLocale(name, enName);


      return {
        ...item,
        label,
        value: code,
      };
    });
  }
}

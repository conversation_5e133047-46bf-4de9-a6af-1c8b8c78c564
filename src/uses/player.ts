export function usePlayer() {
  let playbackNode: AudioWorkletNode | null = null;

  async function init(sampleRate: number = 24000) {
    const audioContext = new AudioContext({ sampleRate });
    // 根据环境确定 worklet 文件路径
    const workletPath = process.env.NODE_ENV === 'production'
      ? './static/audio-playback-worklet.js'
      : 'audio-playback-worklet.js';
    await audioContext.audioWorklet.addModule(workletPath);

    playbackNode = new AudioWorkletNode(audioContext, 'audio-playback-worklet');
    playbackNode.connect(audioContext.destination);
  }

  function play(buffer: Int16Array) {
    if (playbackNode) {
      playbackNode.port.postMessage(buffer);
    }
  }

  function stop() {
    if (playbackNode) {
      playbackNode.port.postMessage(null);
    }
  }

  return {
    init,
    play,
    stop,
  };
}

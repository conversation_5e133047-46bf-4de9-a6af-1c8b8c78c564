/**
 * OpenAI Realtime连接实现
 * 基于BaseConnection实现OpenAI Realtime API的WebSocket连接
 */

import { BaseConnection, type ConnectionFactory } from './base-connection';
import { useWebSocket } from '../../websocket';

/**
 * OpenAI Realtime连接实现
 */
export class OpenAIRealtimeConnection extends BaseConnection {
  private webSocket: ReturnType<typeof useWebSocket> | null = null;

  /**
   * 连接到OpenAI Realtime服务
   */
  async connect(): Promise<void> {
    if (this.isConnected()) {
      return;
    }

    this.setState(Conversation.ConnectionState.CONNECTING);

    try {
      // 创建WebSocket连接
      this.webSocket = useWebSocket({
        wsEndpoint: this.config.endpoint,
        autoConnect: false,
        maxRetries: 0, // 由BaseConnection管理重试
        reconnectDelay: 0,
        onOpen: () => {
          this.setState(Conversation.ConnectionState.CONNECTED);
          this.resetRetryCount();
          this.emit('connected');
        },
        onClose: () => {
          this.setState(Conversation.ConnectionState.DISCONNECTED);
          this.emit('disconnected');
        },
        onError: (event: Event) => {
          this.handleError(new Error(`WebSocket error: ${event.type}`));
        },
        onMessage: (event: MessageEvent) => {
          this.handleMessage(event);
        },
      });

      // 手动触发连接
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, this.config.timeout || 10000);

        const onConnected = () => {
          clearTimeout(timeout);
          this.off('connected', onConnected);
          this.off('error', onError);
          resolve();
        };

        const onError = (error: Error) => {
          clearTimeout(timeout);
          this.off('connected', onConnected);
          this.off('error', onError);
          reject(error);
        };

        this.on('connected', onConnected);
        this.on('error', onError);
      });

    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.webSocket) {
      // 这里应该调用webSocket的关闭方法
      // 由于useWebSocket的实现细节，我们需要适配
      this.webSocket = null;
    }
    this.setState(Conversation.ConnectionState.DISCONNECTED);
    this.cleanup();
  }

  /**
   * 发送消息到OpenAI Realtime服务
   */
  async sendMessage(message: unknown): Promise<boolean> {
    if (!this.isConnected() || !this.webSocket) {
      console.warn('[OpenAIRealtimeConnection] Cannot send message: not connected');
      return false;
    }

    try {
      return this.webSocket.sendJsonMessage(message);
    } catch (error) {
      console.error('[OpenAIRealtimeConnection] Failed to send message:', error);
      return false;
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);
      this.emit('message', message);
      
      // 根据消息类型触发特定事件
      this.dispatchRealtimeEvent(message);
    } catch (error) {
      console.error('[OpenAIRealtimeConnection] Failed to parse message:', error);
      this.emit('parseError', error, event.data);
    }
  }

  /**
   * 分发Realtime特定事件
   */
  private dispatchRealtimeEvent(message: Realtime.Message): void {
    const eventType = message.type;
    
    // 映射Realtime事件到标准化事件
    switch (eventType) {
      // 会话相关
      case 'session.created':
        this.emit('sessionCreated', message);
        break;
      case 'session.updated':
        this.emit('sessionUpdated', message);
        break;

      // 用户音频相关
      case 'input_audio_buffer.speech_started':
        this.emit('userSpeechStart');
        break;
      case 'input_audio_buffer.speech_stopped':
        this.emit('userSpeechEnd', message);
        break;
      case 'conversation.item.input_audio_transcription.completed':
        if ('transcript' in message && 'item_id' in message) {
          this.emit('userTranscriptionComplete', message.transcript, message.item_id);
        }
        break;

      // AI响应相关
      case 'conversation.item.created':
        this.emit('conversationItemCreated', message);
        break;
      case 'response.audio.delta':
        if ('delta' in message && 'item_id' in message) {
          this.emit('aiAudioDelta', message.delta, message.item_id);
        }
        break;
      case 'response.audio_transcript.delta':
        if ('delta' in message && 'item_id' in message) {
          this.emit('aiTranscriptDelta', message.delta, message.item_id);
        }
        break;
      case 'response.text.delta':
        if ('delta' in message && 'item_id' in message) {
          this.emit('aiTextDelta', message.delta, message.item_id);
        }
        break;
      case 'response.done':
        this.emit('responseDone', message);
        break;

      // 工具调用相关
      case 'RUN_STARTED':
        if ('run_id' in message) {
          this.emit('toolCallStart', message.run_id);
        }
        break;
      case 'RUN_FINISHED':
        if ('run_id' in message) {
          this.emit('toolCallComplete', message.run_id);
        }
        break;

      // 自定义事件
      case 'CUSTOM':
        if ('name' in message && 'value' in message) {
          this.emit('customEvent', message.name, message.value);
        }
        break;

      // 错误处理
      case 'error':
        this.emit('realtimeError', message);
        break;

      default:
        // 未处理的消息类型
        this.emit('unhandledMessage', eventType, message);
        break;
    }
  }

  /**
   * 发送会话更新命令
   */
  async updateSession(sessionConfig: Partial<Realtime.SessionUpdateCommand['session']>): Promise<boolean> {
    const command: Realtime.SessionUpdateCommand = {
      type: 'session.update',
      session: sessionConfig,
    };
    return this.sendMessage(command);
  }

  /**
   * 添加用户音频数据
   */
  async addUserAudio(audioData: string): Promise<boolean> {
    const command: Realtime.InputAudioBufferAppendCommand = {
      type: 'input_audio_buffer.append',
      audio: audioData,
    };
    return this.sendMessage(command);
  }

  /**
   * 清空音频缓冲区
   */
  async clearAudioBuffer(): Promise<boolean> {
    const command: Realtime.InputAudioBufferClearCommand = {
      type: 'input_audio_buffer.clear',
    };
    return this.sendMessage(command);
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(text: string): Promise<boolean> {
    // 创建对话项
    const createItemCommand: Realtime.ConversationItemCreateCommand = {
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user' as RoleType,
        content: [{
          type: 'input_text',
          text,
        }],
      },
    };

    const itemSuccess = await this.sendMessage(createItemCommand);
    if (!itemSuccess) {
      return false;
    }

    // 创建响应
    const createResponseCommand: Realtime.ResponseCreateCommand = {
      type: 'response.create',
    };

    return this.sendMessage(createResponseCommand);
  }

  /**
   * 截断对话项（用于打断AI回复）
   */
  async truncateConversationItem(itemId: string, contentIndex = 0, audioEndMs = 0): Promise<boolean> {
    const command: Realtime.ConversationItemTruncateCommand = {
      type: 'conversation.item.truncate',
      item_id: itemId,
      content_index: contentIndex,
      audio_end_ms: audioEndMs,
    };
    return this.sendMessage(command);
  }
}

/**
 * OpenAI Realtime连接工厂
 */
export class OpenAIRealtimeConnectionFactory implements ConnectionFactory {
  createConnection(config: Conversation.ConnectionConfig): BaseConnection {
    return new OpenAIRealtimeConnection(config);
  }

  getSupportedProtocols(): string[] {
    return ['openai-realtime', 'openai'];
  }
}

import { defineStore } from 'pinia';
import _ from 'lodash';

import { DataTags } from '@/consts/data-tags';
import { textByLocale } from '@/helps/locale';

import { DictApi } from '@/api/dict';

// 数据格式化函数
function formatData(data: any[]) {
  return data.map((item) => ({
    ...item,
    label: textByLocale(item.name, item.enName, true),
  }));
}

/**
 * 根据key创建数据字典值
 * @param getKeyFn - 获取 DataTags 的函数
 */
export function createStoreByDatasTags(getKeyFn: (key: typeof DataTags) => string) {
  const key = getKeyFn(DataTags);
  if (!key) {
    throw new Error('key is required');
  }

  const initialState = {
    data: [] as any[],
    loading: false,
    loaded: false,
  };

  // getters 定义
  const getters = {
    getTextByCode(state: typeof initialState) {
      return function getTextByCode(code: any, defaultStr?: string | null) {
        const match = _.find(state.data, { code });
        return match ? match.label : defaultStr ?? code;
      };
    },

    getZhTextByCode(state: typeof initialState) {
      return function getTextByCode(code: any, defaultStr?: string | null) {
        const match = _.find(state.data, { code });
        return match ? match.name || match.enName : defaultStr ?? code;
      };
    },

    getEnTextByCode(state: typeof initialState) {
      return function getTextByCode(code: any, defaultStr?: string | null) {
        const match = _.find(state.data, { code });
        return match ? match.enName || match.name : defaultStr ?? code;
      };
    },

    findTarget(state: typeof initialState) {
      return function findTarget(code: any, searchKey = 'code') {
        return state.data.find((item: Record<string, any>) => item[searchKey] === code);
      };
    },
  };

  // actions 定义
  const actions = {
    async loadDataIfNeeded() {
      if (initialState.loading || initialState.loaded) {
        return;
      }

      initialState.loading = true;
      try {
        const api = new DictApi();
        api.params = {
          key,
        };
        const res = await api.sendWithSpecifyType();
        initialState.data = formatData(res);
        initialState.loaded = true;
      } finally {
        initialState.loading = false;
      }
    },
  };

  // 定义 store
  return defineStore(key, {
    state: () => initialState,
    getters,
    actions,
  });
}
